-- 三角洲行动自动化脚本 - 定制简化版 V3.4.3 (修复模块加载)
import('java.io.File')
import('java.lang.*')
import('java.util.Arrays')
import('android.content.Context')
import('android.hardware.Sensor')
import('android.hardware.SensorEvent')
import('android.hardware.SensorEventListener')
import('android.hardware.SensorManager')
import('com.nx.assist.lua.LuaEngine')

-- =================== 第一步：加载核心框架 ===================
local 框架加载状态, GameScript = pcall(require, "核心框架")
if not 框架加载状态 then
    print("❌ 核心框架加载失败，将使用传统模式运行: " .. tostring(GameScript))
    -- 创建最小化的框架接口以保证脚本可以继续运行
    GameScript = {
        version = "3.4.3 (兼容模式)",
        modules = {},
        state = {},
        loadModule = function(name, required) return require(name) end,
        setState = function(key, value) if _G.State then _G.State[key] = value end return value end,
        getState = function(key, default) return _G.State and _G.State[key] or default end,
        safeGetNumber = function(val, default) return tonumber(val) or default or 0 end,
        registerGlobal = function(name, value) _G[name] = value return value end,
        init = function() return GameScript end
    }
else
    print("✅ 核心框架加载成功，版本: " .. GameScript.version)
end

-- 初始化框架
GameScript.init()

-- =================== 第二步：在脚本开头加载日志系统 ===================
-- 使用框架加载日志系统
local Logger = GameScript.loadModule("日志系统", false)
if Logger then
    Logger.init()
    -- 注册到框架和全局环境
    GameScript.modules.Logger = Logger
    GameScript.registerGlobal("Logger", Logger, "核心框架")
else
    print("❌ 日志系统加载失败，将使用基础日志")
    -- 创建简化的日志接口
    Logger = {
        info = function(module, msg) print(string.format("[INFO][%s] %s", module, msg)) end,
        error = function(module, msg) print(string.format("[ERROR][%s] %s", module, msg)) end,
        warn = function(module, msg) print(string.format("[WARN][%s] %s", module, msg)) end,
        debug = function(module, msg) print(string.format("[DEBUG][%s] %s", module, msg)) end,
        fatal = function(module, msg) print(string.format("[FATAL][%s] %s", module, msg)) end,
        performance = function(module, op, duration) print(string.format("[PERF][%s] %s: %dms", module, op, duration)) end,
        stateChange = function(module, from, to, reason) print(string.format("[STATE][%s] %s -> %s (%s)", module, from, to, reason)) end,
        systemSnapshot = function(module) print(string.format("[SNAP][%s] System snapshot requested.", module)) end,
        getStats = function() return {} end,
        init = function() print("简化日志系统已初始化") end,
        flush = function() end,
        sessionEnd = function() print("会话结束") end
    }
    -- 注册到框架和全局环境
    GameScript.modules.Logger = Logger
    GameScript.registerGlobal("Logger", Logger, "核心框架")
    Logger.init()
end

-- =================== 脚本启动和结束处理函数（提前定义） ===================
-- 在脚本开始时
local function 脚本启动处理()
    Logger.info("Script", "=== 三角洲行动脚本启动 ===")
    Logger.info("Script", "版本: " .. GameScript.version)
    
    -- 记录系统信息
    Logger.info("Script", "系统信息", {
        lua_version = _VERSION,
        memory_kb = math.floor(collectgarbage("count")),
        timestamp = os.time()
    })
    
    -- 初始化状态面板时间戳，防止启动时重复显示
    if _G.State then
        _G.State.上次状态面板调用时间 = os.time()
    end
end

-- 脚本结束时的清理
local function 脚本结束处理()
    Logger.info("Script", "脚本正在退出")
    
    -- 记录最终统计
    local stats = Logger.getStats()
    Logger.info("Script", "最终统计数据", stats)
    
    -- 清理全局变量
    GameScript.cleanupGlobals()
    
    -- 结束会话
    Logger.sessionEnd()
    
    -- 强制刷新日志
    Logger.flush()
    
    print("✅ 日志已保存到: " .. (Logger.config and Logger.config.logPath or "/storage/emulated/0/Download/脚本日志.txt"))
end

-- =================== 核心工具函数提前定义 ===================
-- 统一的显示信息输出接口
local function 显示信息(msg, 显示Toast)
    local 时间戳 = os.date("%Y-%m-%d %H:%M:%S")
    local 格式化消息 = string.format("[%s] %s", 时间戳, msg)
    print(格式化消息)
    
    if 显示Toast ~= false then
        toast(msg)
    end
    
    -- 记录到日志文件
    if Logger then
        if string.find(msg, "错误") or string.find(msg, "失败") or string.find(msg, "❌") or string.find(msg, "!!") then
            Logger.error("Display", msg)
        elseif string.find(msg, "警告") or string.find(msg, "⚠️") then
            Logger.warn("Display", msg)
        else
            Logger.info("Display", msg)
        end
    end
end
-- 【修复】只注册到框架，减少全局变量污染
GameScript.modules.显示信息 = 显示信息
-- GameScript.registerGlobal("显示信息", 显示信息, "核心框架") -- 移除全局注册

-- 查找并点击功能 (提前定义以确保所有模块可用)
local function 查找并点击(x1, y1, x2, y2, pic, color, threshold, 等待时间, 描述)
    等待时间 = 等待时间 or 1000
    描述 = 描述 or pic
    
    local index, x, y = findPic(x1, y1, x2, y2, pic, color or "101010", 0, threshold or 0.7)
    if x > -1 and y > -1 then
        tap(x, y)
        if 描述 then
            显示信息("✓ 找到并点击了" .. 描述)
        end
        sleep(等待时间)
        return true, x, y
    end
    if 描述 then
        显示信息("✗ 未找到" .. 描述, false)
    end
    return false, -1, -1
end
-- 【修复】只注册到框架，减少全局变量污染
GameScript.modules.查找并点击 = 查找并点击
-- GameScript.registerGlobal("查找并点击", 查找并点击, "核心框架") -- 移除全局注册

-- 安全读取UI配置值
local function 安全读取UI值(配置表, 键名, 默认值)
    if not 配置表 or 配置表[键名] == nil then
        return 默认值
    end
    return 配置表[键名]
end
-- 【修复】只注册到框架，减少全局变量污染
GameScript.modules.安全读取UI值 = 安全读取UI值
-- GameScript.registerGlobal("安全读取UI值", 安全读取UI值, "核心框架") -- 移除全局注册

-- 转换布尔值
local function 转换为布尔值(值)
    if type(值) == "boolean" then
        return 值
    elseif type(值) == "string" then
        local lower_val = string.lower(值)
        return lower_val == "true" or lower_val == "1" or lower_val == "yes" or lower_val == "on"
    elseif type(值) == "number" then
        return 值 ~= 0
    else
        return false
    end
end
-- 【修复】只注册到框架，减少全局变量污染
GameScript.modules.转换为布尔值 = 转换为布尔值
-- GameScript.registerGlobal("转换为布尔值", 转换为布尔值, "核心框架") -- 移除全局注册

-- 安全的数值比较函数 - 新增
local function 安全比较(值1, 值2, 默认值1, 默认值2)
    -- 使用框架的安全数值获取
    local 数值1 = GameScript.safeGetNumber(值1, 默认值1 or 0)
    local 数值2 = GameScript.safeGetNumber(值2, 默认值2 or 0)
    return 数值1, 数值2
end
-- 【修复】只注册到框架，减少全局变量污染
GameScript.modules.安全比较 = 安全比较
-- GameScript.registerGlobal("安全比较", 安全比较, "核心框架") -- 移除全局注册

-- 【修复】改进的时间戳获取函数 - 使用StateManager的验证器
local function 安全获取时间戳(时间戳变量, 默认偏移)
    默认偏移 = 默认偏移 or 0

    -- 如果是nil，返回当前时间加偏移
    if 时间戳变量 == nil then
        return os.time() + 默认偏移
    end

    -- 【修复】使用StateManager的时间戳验证器
    if _G.StateManager and _G.StateManager.StateValidators then
        local valid, validated_value = _G.StateManager.StateValidators.时间戳(时间戳变量)
        if valid then
            return validated_value
        else
            if Logger then
                Logger.warn("TimeHandler", "时间戳验证失败: " .. tostring(validated_value))
            end
            return os.time() + 默认偏移
        end
    else
        -- 降级处理：使用原有逻辑
        local 数值 = GameScript.safeGetNumber(时间戳变量)
        if 数值 == 0 then
            if Logger then
                Logger.warn("TimeHandler", "时间戳格式错误，使用当前时间")
            end
            return os.time() + 默认偏移
        end
        return 数值
    end
end
-- 【修复】只注册到框架，减少全局变量污染
GameScript.modules.安全获取时间戳 = 安全获取时间戳
-- GameScript.registerGlobal("安全获取时间戳", 安全获取时间戳, "核心框架") -- 移除全局注册

-- =================== 【关键修复】统一模块加载 ===================
-- 使用框架的模块加载函数加载所有模块

-- 【新增】加载状态管理模块
显示信息("🔄 正在加载状态管理模块...")
GameScript.modules.StateManager = GameScript.loadModule("状态管理", true)
if not GameScript.modules.StateManager then
    显示信息("❌ 状态管理模块加载失败，脚本终止。")
    return
end

-- 创建全局状态管理器实例
_G.StateManager = GameScript.modules.StateManager.create()
显示信息("✅ 状态管理器初始化完成")

-- 模块加载顺序（按依赖关系排序）
local 模块加载顺序 = {
    "工具函数",     -- 基础工具函数，几乎所有模块都依赖
    "OCR功能",      -- OCR功能，多个模块依赖
    "扫货抢购",     -- 扫货功能
    "房卡抢购",     -- 房卡功能
    "五级蛋抢购",   -- 五级蛋功能
    "出售子弹",     -- 出售子弹功能
    "滚仓功能",     -- 滚仓功能
    "特勤处制造",   -- 特勤处制造功能
    "单机抢购模式", -- 单机抢购模式功能
    "自动重启游戏", -- 自动重启游戏功能
}

-- 按顺序加载模块
显示信息("🔄 开始按依赖顺序加载模块...")

-- 核心依赖 (必需)
GameScript.modules.工具函数 = GameScript.loadModule("工具函数", true)
GameScript.modules.OCR功能 = GameScript.loadModule("OCR功能", true)

-- 【修复】减少全局变量注册，只注册核心模块
-- GameScript.registerGlobal("工具函数", GameScript.modules.工具函数, "核心框架") -- 移除全局注册
-- GameScript.registerGlobal("OCR功能", GameScript.modules.OCR功能, "核心框架") -- 移除全局注册

-- 如果核心模块加载失败，则终止脚本
if not GameScript.modules.工具函数 or not GameScript.modules.OCR功能 then
    显示信息("❌ 核心依赖 (工具函数或OCR) 加载失败，脚本终止。")
    return
end

-- 功能模块 (必需和可选)
GameScript.modules.扫货抢购模块 = GameScript.loadModule("扫货抢购", true)
GameScript.modules.房卡抢购模块 = GameScript.loadModule("房卡抢购", true)
GameScript.modules.五级蛋抢购模块 = GameScript.loadModule("五级蛋抢购", false) -- 新增：加载五级蛋模块
GameScript.modules.出售子弹模块 = GameScript.loadModule("出售子弹", true)
GameScript.modules.滚仓模块 = GameScript.loadModule("滚仓功能", false)
GameScript.modules.特勤处制造模块 = GameScript.loadModule("特勤处制造", false)
GameScript.modules.自动重启游戏 = GameScript.loadModule("自动重启游戏", false)
-- 【延迟加载】网络验证模块 - 只在需要时才加载
GameScript.modules.网络验证模块 = nil

-- 【修复】减少全局变量污染，模块通过GameScript.modules访问
-- 只保留必要的向后兼容性全局变量
if GameScript.modules.特勤处制造模块 then
    _G.特勤处制造 = GameScript.modules.特勤处制造模块 -- 保留这个，因为其他模块可能依赖
end
-- 其他模块不再注册为全局变量，通过GameScript.modules访问

-- 在模块加载部分添加单机抢购模式
GameScript.modules.单机抢购模式模块 = GameScript.loadModule("单机抢购模式", false)

-- 【修复】减少全局变量污染
-- GameScript.registerGlobal("单机抢购模式模块", GameScript.modules.单机抢购模式模块, "核心框架") -- 移除全局注册

print("✅ 所有模块加载完成。")

-- =================== 新增：智能游戏状态检查与启动函数 ===================
-- 修复后的检查并启动游戏函数
function 检查并启动游戏()
    -- 使用框架的错误处理机制
    local ErrorType = GameScript.ErrorHandler.ErrorType
    
    -- 【修复】移除了阻止节能模式下启动游戏的逻辑
    -- 原来的代码会错误地阻止从节能模式唤醒后的游戏启动
    
    if _G.State and _G.State.isGameClosed == false then
        -- 如果状态记录为游戏已打开，则快速检查
        -- 但还需要验证游戏是否真的在运行
        if GameScript.modules.工具函数 and GameScript.modules.工具函数.尝试返回大厅 then
            -- 使用错误处理包装尝试返回大厅函数
            local result_ok, result = GameScript.ErrorHandler.safeCall(
                GameScript.modules.工具函数.尝试返回大厅,
                ErrorType.UI,
                "游戏状态验证",
                "游戏状态验证", true
            )
            
            if result_ok and result then
                显示信息("✓ 游戏已在运行。")
                return true
            else
                显示信息("⚠️ 游戏状态不一致，需要重新启动...")
                -- 游戏状态不一致，继续执行启动逻辑
            end
        end
    end
    
    显示信息("→ 检查游戏状态...")
    
    -- 尝试通过返回大厅来探测游戏是否在前台并响应
    if GameScript.modules.工具函数 and GameScript.modules.工具函数.尝试返回大厅 then
        local result_ok, result = GameScript.ErrorHandler.safeCall(
            GameScript.modules.工具函数.尝试返回大厅,
            ErrorType.UI,
            "游戏状态检查",
            "游戏状态检查", true
        )
        
        if result_ok and result then
            显示信息("✓ 游戏已在运行。")
            if _G.State then 
                GameScript.setState("isGameClosed", false, "游戏运行检查成功")
            end
            return true
        end
    end
    
    显示信息("✗ 游戏未运行或无响应，正在启动...")
    if GameScript.modules.自动重启游戏 and type(GameScript.modules.自动重启游戏) == "function" then
        -- 使用重试机制启动游戏，最多尝试2次，间隔5秒
        local start_ok, start_result = GameScript.ErrorHandler.retryCall(
            GameScript.modules.自动重启游戏,
            2, 5000,
            ErrorType.RESOURCE,
            "游戏启动"
        )
        
        if start_ok then
            显示信息("✓ 游戏启动成功。等待5秒加载...")
            if _G.State then 
                GameScript.setState("isGameClosed", false, "游戏启动成功")
            end
            sleep(5000) -- 等待游戏加载
            return true
        else
            显示信息("❌ 游戏启动失败: " .. tostring(start_result))
            return false
        end
    else
        显示信息("❌ 自动重启游戏函数不可用，无法启动游戏！")
        return false
    end
end
_G.检查并启动游戏 = 检查并启动游戏

-- =================== 简化的UI配置提取 ===================
-- 从UI界面提取当前配置
local function 从UI提取配置(界面配置)
	local ui_page0 = 界面配置.page0 or {}
	local ui_page1 = 界面配置.page1 or {}
	
	return {
		多选框_选择扫货功能 = 安全读取UI值(ui_page0, "多选框_选择扫货功能", false),
		多选框_选择房卡功能 = 安全读取UI值(ui_page0, "多选框_选择房卡功能", false),
		多选框_选择五级蛋 = 安全读取UI值(ui_page0, "多选框_选择五级蛋", false),
		多选框_出售子弹 = 安全读取UI值(ui_page0, "多选框_出售子弹", false),
		多选框_单机抢购模式 = 安全读取UI值(ui_page0, "多选框_单机抢购模式", false),
		扫货_自动挂机 = 安全读取UI值(ui_page0, "扫货_自动挂机", false),
		特勤处_制作装备 = 安全读取UI值(ui_page0, "特勤处_制作装备", false),
		子弹类型 = 安全读取UI值(ui_page0, "子弹类型", "0"),
		特勤处制造类型 = 安全读取UI值(ui_page0, "特勤处制造类型", "0"),
		
		输入框_扫货输入价格 = 安全读取UI值(ui_page1, "输入框_扫货输入价格", "630"),
		输入框_房卡输入价格 = 安全读取UI值(ui_page1, "输入框_房卡输入价格", "630"),
		输入框_五级蛋价格 = 安全读取UI值(ui_page1, "输入框_五级蛋价格", "150000"),
		输入框_抢购次数 = 安全读取UI值(ui_page1, "输入框_抢购次数", "1"),
		输入框_单机抢购价格 = 安全读取UI值(ui_page1, "输入框_单机抢购价格", "0"), -- 新增：读取单机抢购价格
		输入框_资金保护 = 安全读取UI值(ui_page1, "输入框_资金保护", "0"), -- 新增：读取资金保护阈值
		输入框_出售价格 = 安全读取UI值(ui_page1, "输入框_出售价格", "758"),
		出售_开始时间 = 安全读取UI值(ui_page1, "出售_开始时间", "13"),
		出售_结束时间 = 安全读取UI值(ui_page1, "出售_结束时间", "19"),
	}
end

-- =================== 【修复】统一状态管理 ===================
-- 使用StateManager替代本地State对象，消除状态管理分散问题

-- 【修复】创建状态管理器的便捷访问接口
local State = {}

-- 创建元表，避免递归
local StateMetatable = {
    __index = function(t, key)
        if _G.StateManager then
            return _G.StateManager:get(key)
        else
            return nil
        end
    end,
    __newindex = function(t, key, value)
        if _G.StateManager then
            _G.StateManager:set(key, value, "State接口设置")
        end
    end
}
setmetatable(State, StateMetatable)

-- 【新增】状态变化记录函数 - 现在使用StateManager
local function 更新状态(字段名, 新值, 原因)
    return _G.StateManager:set(字段名, 新值, 原因 or "程序逻辑")
end

-- 【修复】立即初始化关键时间戳 - 使用StateManager
local function 初始化状态时间戳()
	local 当前时间戳 = os.time()
	-- 使用StateManager的批量更新功能
	_G.StateManager:batch({
		脚本启动时间 = 当前时间戳,
		下次状态更新时间 = 当前时间戳 + 5,
		上次重启完成时间 = 当前时间戳,
		上次重启检查时间 = 当前时间戳,
		上次状态计算时间 = 当前时间戳,
		当前动作描述 = "脚本初始化中..."
	}, "初始化状态时间戳")
	显示信息("✓ 状态时间戳初始化完成", false)
end
初始化状态时间戳()

-- 设置全局State接口，保持向后兼容
_G.State = State

-- =================== 简化的UI与配置处理 ===================
-- 加载UI界面
显示信息("=== 三角洲行动自动化脚本启动 ===")
显示信息("版本：定制简化版 V3.4.3 (修复模块加载)")

-- 【简单开关】控制是否执行网络验证 - 修改这一行即可
local 启用网络验证 = false  -- true=启用验证, false=跳过验证

if 启用网络验证 then
    -- 【延迟加载】只在需要时才加载网络验证模块
    if not GameScript.modules.网络验证模块 then
        显示信息("🔄 正在加载网络验证模块...")
        GameScript.modules.网络验证模块 = GameScript.loadModule("网络验证", true)
        if GameScript.modules.网络验证模块 then
            显示信息("✅ 网络验证模块加载成功")
        else
            显示信息("❌ 网络验证模块加载失败")
            return
        end
    end
    
    -- 在网络验证模块执行前添加调试代码
    print("=== 网络验证调试信息 ===")
    print("网络验证模块类型：", type(GameScript.modules.网络验证模块))
    print("网络验证模块内容：", GameScript.modules.网络验证模块)

    -- 检查全局app变量
    if _G.app then
        print("全局app存在：", true)
        print("app.success：", _G.app.success)
        print("app.token：", _G.app.token and "存在" or "不存在")
        print("app.appId：", _G.app.appId)
    else
        print("全局app存在：", false)
    end

    -- 检查模块的具体内容（如果是表类型）
    if type(GameScript.modules.网络验证模块) == "table" then
        print("模块表的键：")
        for k, v in pairs(GameScript.modules.网络验证模块) do
            print("  ", k, type(v))
        end
    end

    print("=== 调试信息结束 ===")

    -- 执行网络验证
    if GameScript.modules.网络验证模块 then
    显示信息("...正在执行网络验证...")
    
    -- 特殊情况处理：如果网络验证模块是布尔值true
    if type(GameScript.modules.网络验证模块) == "boolean" then
        -- 网络验证模块已经通过showUI执行了验证
        -- 检查全局app变量来判断验证结果
        if _G.app and _G.app.success == true then
            显示信息("✅ 网络验证已成功完成（通过UI界面）")
        else
            显示信息("❌ 网络验证失败：未找到有效的验证结果")
            return -- 验证失败，终止脚本
        end
    else
        -- 原有逻辑，尝试调用模块
        local 验证成功, 验证结果 = true, nil
        
        -- 【修复】使用统一错误处理机制
        if type(GameScript.modules.网络验证模块) == "function" then
            验证成功, 验证结果 = GameScript.ErrorHandler.safeCall(
                GameScript.modules.网络验证模块,
                GameScript.ErrorHandler.ErrorType.NETWORK,
                "网络验证"
            )
        elseif type(GameScript.modules.网络验证模块) == "table" then
            -- 表类型模块，直接使用返回的验证结果
            验证成功 = true
            验证结果 = GameScript.modules.网络验证模块
        else
            验证成功 = false
            验证结果 = "网络验证模块格式不正确"
        end
        
        if not 验证成功 then
            显示信息("❌ 网络验证失败：" .. tostring(验证结果))
            return -- 验证失败，终止脚本
        end
        
        显示信息("✅ 网络验证成功")
    end
else
    显示信息("❌ 错误：网络验证模块未加载，脚本终止。")
    return -- 模块未加载，终止脚本
end
else
    -- 跳过网络验证，直接继续执行
    显示信息("⏭️ 网络验证已跳过，直接执行任务...")
end

-- 【修复】使用统一错误处理加载UI
local 界面配置状态, 界面配置原始 = GameScript.ErrorHandler.safeCall(
    showUI,
    GameScript.ErrorHandler.ErrorType.UI,
    "UI配置加载",
    "三角洲行动定制.ui"
)
if not 界面配置状态 then
	显示信息("错误：无法加载UI配置文件，脚本将退出。")
	return
end

--显示信息("✓ UI界面加载成功！请设置您需要的功能选项，然后点击确认。")

-- 【修复】使用统一错误处理解析UI配置
local 界面配置 = {}
local 界面配置解析状态, 界面配置解析结果 = GameScript.ErrorHandler.safeCall(
    jsonLib.decode,
    GameScript.ErrorHandler.ErrorType.LOGIC,
    "UI配置解析",
    界面配置原始
)
if not 界面配置解析状态 then
	显示信息("错误：UI配置解析失败，脚本将退出。")
	return
else
	界面配置 = 界面配置解析结果
end

-- 从UI提取当前配置
local 当前配置 = 从UI提取配置(界面配置)

-- =================== 配置验证与功能互斥检查 ===================
local function 验证配置合理性(配置)
    local 验证结果 = {成功 = true, 错误信息 = {}}
    
    -- 功能互斥性检查
    local 功能计数 = 0
    local 已选功能 = {}
    
    -- 检查主要功能状态
    local 功能状态 = {
        扫货 = 转换为布尔值(配置.多选框_选择扫货功能),
        房卡 = 转换为布尔值(配置.多选框_选择房卡功能),
        五级蛋 = 转换为布尔值(配置.多选框_选择五级蛋),
        出售 = 转换为布尔值(配置.多选框_出售子弹),
        制造 = 转换为布尔值(配置.特勤处_制作装备)
    }
    
    -- 统计已启用的功能
    for 功能名, 状态 in pairs(功能状态) do
        if 状态 then
            功能计数 = 功能计数 + 1
            table.insert(已选功能, 功能名)
        end
    end
    
    -- 检查功能互斥
    if 功能计数 > 1 then
        table.insert(验证结果.错误信息, string.format(
            "检测到多个功能同时开启（%s），请只选择一个主要功能",
            table.concat(已选功能, "、")
        ))
        验证结果.成功 = false
    end
    
    -- 验证价格设置
    local function 验证价格(价格, 名称, 最小值, 最大值)
        local 数值 = tonumber(价格)
        if not 数值 then
            table.insert(验证结果.错误信息, string.format("%s必须是有效的数字", 名称))
            验证结果.成功 = false
        elseif 数值 < 最小值 or 数值 > 最大值 then
            table.insert(验证结果.错误信息, string.format("%s必须在%d-%d之间", 名称, 最小值, 最大值))
            验证结果.成功 = false
        end
    end
    
    -- 根据功能验证相关配置
    if 功能状态.扫货 then
        验证价格(配置.输入框_扫货输入价格, "扫货价格", 1, 99999999999)
    end
    
    if 功能状态.房卡 then
        验证价格(配置.输入框_房卡输入价格, "房卡价格", 1, 99999999999)
    end
    
    if 功能状态.五级蛋 then
        验证价格(配置.输入框_五级蛋价格, "五级蛋价格", 1, 99999999999)
    end
    
    if 功能状态.出售 then
        验证价格(配置.输入框_出售价格, "出售价格", 1, 99999999999)
        -- 验证子弹类型
        local 子弹类型 = tonumber(配置.子弹类型)
        if not 子弹类型 or 子弹类型 < 0 or 子弹类型 > 4 then
            table.insert(验证结果.错误信息, "无效的子弹类型选择")
            验证结果.成功 = false
        end
    end
    
    if 功能状态.制造 then
        验证价格(配置.输入框_出售价格, "出售价格", 1, 99999999999)
        -- 验证子弹类型
        local 子弹类型 = tonumber(配置.子弹类型)
        if not 子弹类型 or 子弹类型 < 0 or 子弹类型 > 4 then
            table.insert(验证结果.错误信息, "无效的子弹类型选择")
            验证结果.成功 = false
        end
    end
    
    -- 验证时间设置
    local 开始时间 = tonumber(配置.出售_开始时间)
    local 结束时间 = tonumber(配置.出售_结束时间)
    
    if not 开始时间 or not 结束时间 then
        table.insert(验证结果.错误信息, "出售时间必须是有效的数字")
        验证结果.成功 = false
    else
        if 开始时间 < 0 or 开始时间 > 23 or 结束时间 < 0 or 结束时间 > 23 then
            table.insert(验证结果.错误信息, "出售时间必须在0-23之间")
            验证结果.成功 = false
        end
        if 开始时间 == 结束时间 then
            table.insert(验证结果.错误信息, "出售开始时间和结束时间不能相同")
            验证结果.成功 = false
        end
    end
    
    -- 验证重启设置
    if 转换为布尔值(配置.启用定时重启) then
        local 重启间隔 = tonumber(配置.重启间隔小时)
        if not 重启间隔 then
            table.insert(验证结果.错误信息, "重启间隔必须是有效的数字")
            验证结果.成功 = false
        elseif 重启间隔 < 1 or 重启间隔 > 24 then
            table.insert(验证结果.错误信息, "重启间隔必须在1-24小时之间")
            验证结果.成功 = false
        end
    end
    
    return 验证结果
end

local 验证结果 = 验证配置合理性(当前配置)
if not 验证结果.成功 then
    local 错误汇总 = table.concat(验证结果.错误信息, "\n")
    显示信息("错误：检测到不合理的配置，请修正：\n" .. 错误汇总)
    return -- 终止脚本
end

-- =================== 参数初始化（使用当前配置） ===================
-- 参数验证函数
local function 验证数值参数(值, 默认值, 最小值, 最大值, 参数名)
    local 数值 = tonumber(值) or 默认值
    if 最小值 and 数值 < 最小值 then
        显示信息(string.format("⚠️ %s(%s)小于最小值%s，已自动调整", 参数名, 数值, 最小值))
        数值 = 最小值
    end
    if 最大值 and 数值 > 最大值 then
        显示信息(string.format("⚠️ %s(%s)大于最大值%s，已自动调整", 参数名, 数值, 最大值))
        数值 = 最大值
    end
    return 数值
end

-- 数值型参数（带验证）
local 输入框_扫货输入价格_str = 当前配置.输入框_扫货输入价格 or "630"
local 输入框_房卡输入价格_str = 当前配置.输入框_房卡输入价格 or "630"
local 输入框_抢购次数_str = 当前配置.输入框_抢购次数 or "1"
local 输入框_出售价格 = 验证数值参数(当前配置.输入框_出售价格, 758, 1, 9999999999, "出售价格")

-- 初始化状态变量（带验证）
State.抢购次数 = 验证数值参数(输入框_抢购次数_str, 1, 1, 100, "抢购次数")
State.扫货输入价格 = 验证数值参数(输入框_扫货输入价格_str, 630, 1, 9999999999, "扫货价格")
State.房卡输入价格 = 验证数值参数(输入框_房卡输入价格_str, 630, 1, 9999999999, "房卡价格")
State.五级蛋价格 = 验证数值参数(当前配置.输入框_五级蛋价格, 150000, 1, 9999999999, "五级蛋价格")

-- 时间设置（带验证）
local 出售_开始时间 = 验证数值参数(当前配置.出售_开始时间, 13, 0, 23, "出售开始时间")
local 出售_结束时间 = 验证数值参数(当前配置.出售_结束时间, 19, 0, 23, "出售结束时间")

-- 功能开关（带日志）
local function 初始化功能开关(配置值, 功能名称)
    local 状态 = 转换为布尔值(配置值)
    Logger.info("Config", string.format("%s功能状态: %s", 功能名称, 状态 and "开启" or "关闭"))
    return 状态
end

local 扫货功能 = 初始化功能开关(当前配置.多选框_选择扫货功能, "扫货")
local 房卡功能 = 初始化功能开关(当前配置.多选框_选择房卡功能, "房卡")
local 五级蛋功能 = 初始化功能开关(当前配置.多选框_选择五级蛋, "五级蛋")
local 出售子弹 = 初始化功能开关(当前配置.多选框_出售子弹, "出售子弹")
local 单机抢购模式 = 初始化功能开关(当前配置.多选框_单机抢购模式, "单机抢购")
local 自动挂机 = 初始化功能开关(当前配置.扫货_自动挂机, "自动挂机")
local 特勤处制造_功能开关 = 初始化功能开关(当前配置.特勤处_制作装备, "特勤处制造")

-- 【修复】根据功能状态有条件地设置全局别名，避免模块间冲突
if 特勤处制造_功能开关 and GameScript.modules.特勤处制造模块 then
    GameScript.modules.特勤处制造 = GameScript.modules.特勤处制造模块
    显示信息("✓ 特勤处制造功能全局变量已设置", false)
else
    GameScript.modules.特勤处制造 = nil
    显示信息("✓ 特勤处制造功能全局变量已清理", false)
end

-- 功能互斥检查
if 房卡功能 then
    if 扫货功能 or 出售子弹 or 五级蛋功能 then
        显示信息("⚠️ 房卡功能与其他购买功能互斥，已自动关闭其他功能")
        扫货功能 = false
        出售子弹 = false
        五级蛋功能 = false
    end
end
if 五级蛋功能 then
    if 扫货功能 or 出售子弹 or 房卡功能 then
        显示信息("⚠️ 五级蛋功能与其他购买功能互斥，已自动关闭其他功能")
        扫货功能 = false
        出售子弹 = false
        房卡功能 = false
    end
end

-- 设置全局变量
_G.房卡输入价格 = State.房卡输入价格
_G.房卡功能 = 房卡功能

-- 确保出售相关的全局变量可用
_G.出售子弹 = 出售子弹
_G.输入框_出售价格 = 输入框_出售价格
_G.多选框_出售子弹_type = 当前配置.子弹类型 or "0"

-- 初始化重启配置（智能动态设置）
-- 【修改】根据用户要求，仅在勾选"自动挂机"时启用定时重启
State.启用定时重启 = 自动挂机

-- 智能重启间隔计算：根据OCR检测的制作时间和当前任务状态
local function 计算智能重启间隔(已有状态)
    -- 【关键修复】节能模式检查
    if _G.State and _G.State.isGameClosed then
        显示信息("💤 节能模式下，使用缓存的重启间隔", false)
        return _G.State.定时重启间隔 or (4 * 60 * 60)
    end

    local 当前时间戳 = os.time()
    local 当前时间 = os.date("*t")
    local 当前小时 = 当前时间.hour
    
    -- 检查是否在交易时间段
    local 正在扫货时段 = 在时间段内(当前小时, 0, 6)
    local 正在出售时段 = 在时间段内(当前小时, 出售_开始时间, 出售_结束时间)
    
    -- 【修改】无论是全自动还是全自动+特勤处，都在非交易时间段采用长间隔
    if 自动挂机 then
        if 正在扫货时段 or 正在出售时段 then
            return 1 * 60 * 60 -- 交易时间段：1小时重启确保及时响应
        else
            -- 非交易时间段：使用长间隔（让节能模式发挥作用）
            return 8 * 60 * 60 -- 8小时，足够长的间隔让节能模式生效
        end
    end
    
    -- 如果勾选了仅特勤处制造，根据OCR检测的最短制作时间设置重启间隔
    if 特勤处制造_功能开关 and not 自动挂机 then
        -- 在交易时间段不重启
        if 正在扫货时段 or 正在出售时段 then
            return 24 * 60 * 60 -- 24小时（实际上不会重启）
        end
        
        -- 根据制造时间设置间隔的逻辑保持不变
        local 最短制作时间 = 0
        local 最短剩余时间 = nil

        if 已有状态 and 已有状态.最短剩余时间 then
            最短剩余时间 = 已有状态.最短剩余时间
            显示信息("🔄 使用已有状态计算重启间隔...", false)
        elseif GameScript.modules.特勤处制造模块 and GameScript.modules.特勤处制造模块.获取所有设备状态 then
            local ok, _, ocr_最短剩余时间 = pcall(GameScript.modules.特勤处制造模块.获取所有设备状态)
            if ok and ocr_最短剩余时间 then
                最短剩余时间 = ocr_最短剩余时间
            end
        end
        
        if 最短剩余时间 and 最短剩余时间 > 0 then
            最短制作时间 = 最短剩余时间 * 60 -- 转换为秒
            显示信息(string.format("🔄 检测到特勤处最短制造时间：%d分钟，转换为%d秒", 最短剩余时间, 最短制作时间))
        else
            最短制作时间 = 3 * 60 * 60 -- 默认3小时
            显示信息("⚠️ 无法获取特勤处制造时间，使用默认3小时")
        end
        
        -- 重启间隔 = 最短制作时间 + 缓冲时间（10分钟）
        local 重启间隔 = 最短制作时间 + (10 * 60)
        
        -- 确保重启间隔在合理范围内（30分钟-12小时）
        重启间隔 = math.max(30 * 60, math.min(12 * 60 * 60, 重启间隔))
        
        return 重启间隔
    end
    
    -- 如果勾选了其他单功能模式，在交易时间段不重启
    if 扫货功能 or 房卡功能 or 出售子弹 or 五级蛋功能 then
        if 正在扫货时段 or 正在出售时段 then
            return 24 * 60 * 60 -- 24小时（实际上不会重启）
        end
    end
    
    -- 默认情况：4小时
    return 4 * 60 * 60
end

-- 动态更新重启间隔的函数
local function 更新重启间隔(已有状态)
    -- 【关键修复】节能模式检查
    if _G.State and _G.State.isGameClosed then
        显示信息("💤 节能模式下跳过重启间隔更新", false)
        return _G.State.定时重启间隔 or (4 * 60 * 60)
    end

    local 新间隔 = 计算智能重启间隔(已有状态)
    local 旧间隔 = State.定时重启间隔 or 新间隔
    
    if 新间隔 ~= 旧间隔 then
        State.定时重启间隔 = 新间隔
        local 间隔小时 = math.floor(新间隔 / 3600)
        local 间隔分钟 = math.floor((新间隔 % 3600) / 60)
        显示信息(string.format("🔄 重启间隔已更新：%d小时%d分钟", 间隔小时, 间隔分钟))
    end
    
    return 新间隔
end

-- 初始化重启时间基准
State.上次重启完成时间 = os.time() -- 设置脚本启动时间为基准

-- 【修改】仅在启用时才计算和显示重启信息
if State.启用定时重启 then
State.定时重启间隔 = 更新重启间隔() -- 使用智能计算

local 重启间隔小时 = math.floor(State.定时重启间隔 / 3600)
local 重启间隔分钟 = math.floor((State.定时重启间隔 % 3600) / 60)
显示信息(string.format("定时重启已启用，智能间隔%d小时%d分钟，下次重启时间：%s", 
					  重启间隔小时, 重启间隔分钟,
					  os.date("%H:%M:%S", State.上次重启完成时间 + State.定时重启间隔)))
else
    显示信息("定时重启功能已禁用。")
end

-- 滚仓参数初始化
_G.滚仓_子弹数量 = 0
_G.滚仓_子弹价格 = 0
_G.滚仓_总价格 = 0

if 滚仓扫货 then
	local 子弹数量 = tonumber(当前配置.滚仓_子弹数量) or 0
	local 子弹价格 = tonumber(当前配置.滚仓_子弹价格) or 0
	
	显示信息(string.format("从配置读取滚仓参数: 数量=%d, 价格=%d", 子弹数量, 子弹价格))
	
	if 子弹数量 > 0 and 子弹价格 > 0 then
		-- 同时设置State和全局变量
		State.滚仓_子弹数量 = 子弹数量
		State.滚仓_子弹价格 = 子弹价格
		State.滚仓_总价格 = 子弹数量 * 子弹价格
		
		-- 设置全局变量
		_G.滚仓_子弹数量 = 子弹数量
		_G.滚仓_子弹价格 = 子弹价格
		_G.滚仓_总价格 = 子弹数量 * 子弹价格
		
		显示信息(string.format("滚仓配置：数量=%d, 价格=%d, 总价=%d", 
							  State.滚仓_子弹数量, State.滚仓_子弹价格, State.滚仓_总价格))
	else
		显示信息("警告：滚仓参数无效，功能将被禁用")
		滚仓扫货 = false
	end
end

-- =================== 配置信息显示 ===================
local function 生成配置显示信息()
    local 启用功能列表 = {}
    local 配置详情 = {}
    local 功能计数 = 0

    -- 主要功能检查（互斥功能）
    if 扫货功能 then
        功能计数 = 功能计数 + 1
        table.insert(启用功能列表, "🔍扫货")
        table.insert(配置详情, string.format("扫货价格: %d元", State.扫货输入价格))
        if State.抢购次数 > 1 then
            table.insert(配置详情, string.format("抢购次数: %d次", State.抢购次数))
        end
    end

    if 房卡功能 then
        功能计数 = 功能计数 + 1
        table.insert(启用功能列表, "🎴房卡")
        table.insert(配置详情, string.format("房卡价格: %d元", State.房卡输入价格))
        if State.抢购次数 > 1 then
            table.insert(配置详情, string.format("抢购次数: %d次", State.抢购次数))
        end
    end

    if 五级蛋功能 then
        功能计数 = 功能计数 + 1
        table.insert(启用功能列表, "🥚五级蛋")
        table.insert(配置详情, string.format("五级蛋价格: %d元", State.五级蛋价格))
        if State.抢购次数 > 1 then
            table.insert(配置详情, string.format("抢购次数: %d次", State.抢购次数))
        end
    end

    if 出售子弹 then
        功能计数 = 功能计数 + 1
        local 子弹类型名称 = {
            ["0"] = "9.19子弹",
            ["1"] = "5.7子弹",
            ["2"] = ".45ACP子弹",
            ["3"] = "12 Gauge子弹",
			["4"] = ".300 BLk子弹"
        }
        local 当前子弹类型 = 子弹类型名称[tostring(_G.多选框_出售子弹_type)] or "未知类型"
        table.insert(启用功能列表, "💰出售")
        table.insert(配置详情, string.format("出售%s: %d元", 当前子弹类型, 输入框_出售价格))
        if 出售_开始时间 and 出售_结束时间 then
            table.insert(配置详情, string.format("出售时段: %d:00-%d:00", 出售_开始时间, 出售_结束时间))
        end
    end

    if 滚仓扫货 then
        功能计数 = 功能计数 + 1
        table.insert(启用功能列表, "🔄滚仓")
        if State.滚仓_子弹数量 > 0 and State.滚仓_子弹价格 > 0 then
            table.insert(配置详情, string.format("滚仓: %d个×%d元 (总价%d元)", 
                State.滚仓_子弹数量, 
                State.滚仓_子弹价格,
                State.滚仓_总价格))
        else
            table.insert(配置详情, "⚠️ 滚仓参数未设置")
        end
    end

    if 特勤处制造_功能开关 then
        功能计数 = 功能计数 + 1
        local 制造类型名称 = {
            ["0"] = "技术装备",
            ["1"] = "战术装备", 
            ["2"] = "医疗用品",
            ["3"] = "防护装备"
        }
        local 当前制造类型 = 制造类型名称[tostring(当前配置.特勤处制造类型)] or "全部装备"
        table.insert(启用功能列表, "🏭制造")
        table.insert(配置详情, string.format("制造: %s", 当前制造类型))
    end

    -- 自动挂机是特殊功能，可以与其他功能组合
    if 自动挂机 then
        table.insert(启用功能列表, "🤖挂机")
    end

    -- 生成最终的配置信息
    local 信息列表 = {}
				
    -- 添加功能概述
    if #启用功能列表 > 0 then
        table.insert(信息列表, "当前执行: " .. table.concat(启用功能列表, "+"))
    else
        table.insert(信息列表, "⚠️ 未选择任何功能")
        return "⚠️ 未选择任何功能，请检查配置"
    end
    
    -- 添加详细配置
    if #配置详情 > 0 then
        table.insert(信息列表, "详细配置: " .. table.concat(配置详情, " | "))
    end

    -- 添加功能互斥提示
    if 功能计数 > 1 and not 自动挂机 then
        table.insert(信息列表, "⚠️ 检测到多个主要功能同时开启，可能会产生冲突")
    end

    -- 返回最终拼接的信息
    return table.concat(信息列表, "\n")
end

-- 显示配置信息
显示信息(生成配置显示信息())

-- =================== 核心工具函数 ===================
-- 时间段检查
local function 在时间段内(当前小时, 开始时间, 结束时间)
	-- 安全转换为数字
	local 安全当前小时 = tonumber(当前小时) or 0
	local 安全开始时间 = tonumber(开始时间) or 0
	local 安全结束时间 = tonumber(结束时间) or 23
	
	-- 确保时间在有效范围内
	安全当前小时 = math.max(0, math.min(23, 安全当前小时))
	安全开始时间 = math.max(0, math.min(23, 安全开始时间))
	安全结束时间 = math.max(0, math.min(23, 安全结束时间))
	
	if 安全开始时间 <= 安全结束时间 then
		return 安全当前小时 >= 安全开始时间 and 安全当前小时 < 安全结束时间
	else
		return 安全当前小时 >= 安全开始时间 or 安全当前小时 < 安全结束时间
    end
end

-- 状态显示控制
local function 暂停状态显示() State.允许状态显示 = false end
local function 恢复状态显示() State.允许状态显示 = true end
_G.暂停状态显示 = 暂停状态显示
_G.恢复状态显示 = 恢复状态显示

-- 制造检查条件判断
local function 检查是否需要执行特勤处检查()
	if not GameScript.modules.特勤处制造模块 then return false, "特勤处模块未加载" end
	
	local 当前时间戳 = os.time()
	
	if not State.初始检查完成 then
		return true, "首次运行，执行初始检查..."
	end

	-- 检查是否有任务到期（基于动态时间戳）
	if State.下次制造检查时间戳 > 0 and 当前时间戳 >= State.下次制造检查时间戳 then
		return true, "有设备已完成制造，开始新一轮动态检查..."
	end
        
	return false, ""
end
_G.检查是否需要执行特勤处检查 = 检查是否需要执行特勤处检查

-- 增强版检查是否需要执行特勤处检查函数
local function 检查是否需要执行特勤处检查_增强版()
    if not GameScript.modules.特勤处制造模块 then return false, "特勤处模块未加载" end
    
    local 当前时间戳 = os.time()
    
    -- 1. 核心门控：是否正在检查、是否在节能
    if State.特勤处检查锁.正在检查中 then return false, "特勤处检查正在进行中，跳过" end
    if _G.State and _G.State.isGameClosed then return false, "节能模式下跳过特勤处检查" end
    
    -- 2. 优先触发器：首次运行 或 预定任务到期
    if not State.初始检查完成 then return true, "首次运行，执行初始检查..." end
    if State.下次制造检查时间戳 > 0 and 当前时间戳 >= State.下次制造检查时间戳 then
        return true, "有设备已完成制造，开始新一轮动态检查..."
    end
    
    -- 3. 周期性检查触发器：基于统一的冷却时间
    local 上次全面检查时间 = State.上次特勤处全面检查时间 or 0
    local 冷却时间 = State.特勤处检查锁.最小检查间隔 or 180 -- 3分钟
    if (当前时间戳 - 上次全面检查时间) >= 冷却时间 then
        -- 冷却已过，可以触发周期性检查
        if State.当前空闲数量 and State.当前空闲数量 > 0 then
            return true, string.format("检测到%d个空闲设备，执行周期检查", State.当前空闲数量)
        else
            -- 即使没有空闲设备，也应该在冷却后进行一次检查，以防状态出错
            return true, "周期冷却时间已到，执行例行检查"
        end
    end
        
    -- 4. 如果都不满足，则返回冷却中
    return false, string.format("检查冷却中，还需%d秒", 冷却时间 - (当前时间戳 - 上次全面检查时间))
end

-- 执行制造检查
local function 执行特勤处制造检查(check_reason)
    -- 【新增】节能模式检查：如果游戏已关闭，直接跳过检查
    if _G.State and _G.State.isGameClosed then
        显示信息("💤 节能模式下游戏已关闭，跳过特勤处检查")
        Logger.info("特勤处制造", "节能模式下跳过特勤处检查，原因: " .. tostring(check_reason))
        return
    end

    -- 【新增】额外保护：检查是否在节能模式
    if _G.State and _G.State.isGameClosed == true then
        显示信息("💤 检测到节能模式状态，强制跳过特勤处检查")
        Logger.warn("特勤处制造", "检测到节能模式状态，强制跳过特勤处检查")
        return
    end
    
    if not GameScript.modules.特勤处制造模块 or not GameScript.modules.特勤处制造模块.获取所有设备状态 then
        显示信息("错误：特勤处模块不可用，无法执行检查。")
        return
    end
    
    -- 【方案1核心】上锁并开始检查
    State.特勤处检查锁.正在检查中 = true
    显示信息("=== " .. check_reason .. " ===")
    
    -- 【逻辑重构】: 简化流程，先获取状态，再根据状态决定制造
    -- 【修复】步骤1: 使用统一错误处理获取最新的设备状态
    local 最短剩余时间 = nil -- 提前声明，用于传递给重启间隔计算
    local 状态, 设备状态结果 = GameScript.ErrorHandler.safeCall(
        GameScript.modules.特勤处制造模块.获取所有设备状态,
        GameScript.ErrorHandler.ErrorType.OCR,
        "特勤处设备状态获取"
    )

    if not 状态 then
        显示信息("错误：获取设备状态失败 - " .. tostring(设备状态结果))
        -- 【修复】使用StateManager更新状态
        更新状态("特勤处检查锁", {
            正在检查中 = false,
            上次检查完成时间 = 0,
            最小检查间隔 = 180
        }, "设备状态获取失败")
        return
    end

    -- 解析返回的设备状态结果
    local 是否全部在制造, local_最短剩余时间, 制造中数量, new空闲设备列表
    if type(设备状态结果) == "table" then
        是否全部在制造 = 设备状态结果[1]
        local_最短剩余时间 = 设备状态结果[2]
        制造中数量 = 设备状态结果[3]
        new空闲设备列表 = 设备状态结果[4]
    else
        -- 兼容旧的返回格式
        是否全部在制造 = 设备状态结果
        local_最短剩余时间 = nil
        制造中数量 = 0
        new空闲设备列表 = {}
    end
    最短剩余时间 = local_最短剩余时间 -- 捕获返回的值
    
    -- 步骤2: 更新主脚本的状态
    State.工作台都在制造 = 是否全部在制造
    State.当前制造中数量 = 制造中数量 or 0
    local total_devices = (State.当前制造中数量 or 0) + (#new空闲设备列表 or 0)
    if total_devices < 4 then total_devices = 4 end 
    State.当前空闲数量 = total_devices - (State.当前制造中数量 or 0)
    State.空闲设备列表 = new空闲设备列表 or {}
    State.上次特勤处全面检查时间 = os.time()
    State.初始检查完成 = true
    
    -- 步骤3: 如果有空闲设备，则调用新的制造函数
        if new空闲设备列表 and #new空闲设备列表 > 0 then
        显示信息("检测到" .. #new空闲设备列表 .. "个空闲设备，移交制造模块处理...")
            
        if GameScript.modules.特勤处制造模块.为设备列表启动制造 then
            -- 【修复】使用统一错误处理启动制造
            local 制造状态, 制造结果 = GameScript.ErrorHandler.safeCall(
                GameScript.modules.特勤处制造模块.为设备列表启动制造,
                GameScript.ErrorHandler.ErrorType.UI,
                "特勤处制造启动",
                new空闲设备列表
            )
            if not 制造状态 then
                显示信息("制造启动失败：" .. tostring(制造结果))
            else
                显示信息("制造流程执行完成。")
            end
        else
            显示信息("⚠️ '为设备列表启动制造'函数不可用")
        end
            
        -- 制造后重新获取最终状态以更新计时器
        local final_status, final_all_mfg, final_min_time = pcall(GameScript.modules.特勤处制造模块.获取所有设备状态)
        if final_status then
            State.工作台都在制造 = final_all_mfg
            if final_all_mfg and final_min_time and final_min_time > 0 then
                State.下次制造检查时间戳 = os.time() + (final_min_time * 60) + 300
                    end
                end

    elseif 是否全部在制造 then
        -- 如果全部在制造，则根据最短时间设置下一次检查
        if 最短剩余时间 and 最短剩余时间 > 0 then
            local 下次检查时间戳 = os.time() + (最短剩余时间 * 60) + 300 -- 增加缓冲时间到5分钟
            State.下次制造检查时间戳 = 下次检查时间戳
            显示信息(string.format("所有设备都在制造中，设置下次检查时间为：%s", os.date("%H:%M:%S", 下次检查时间戳)))
            else
            显示信息("所有设备都在制造中，但无法获取剩余时间，将在15分钟后重新检查")
            State.下次制造检查时间戳 = os.time() + (15 * 60)
            end
        else
        显示信息("所有设备都在制造中，且无空闲设备可操作。")
    end
        
    -- 【方案1核心】解锁并更新时间
    State.特勤处检查锁.正在检查中 = false
    -- State.特勤处检查锁.上次检查完成时间 = os.time()
    显示信息("=== 制造检查完成 ===")
    
    -- 更新重启间隔（基于最新的制造时间）
    if 特勤处制造_功能开关 then
        -- 【优化】传递刚刚获取的状态，避免重复检查
        local 新间隔 = 更新重启间隔({最短剩余时间 = 最短剩余时间})
        local 间隔小时 = math.floor(新间隔 / 3600)
        local 间隔分钟 = math.floor((新间隔 % 3600) / 60)
        显示信息(string.format("🔄 根据制造时间更新重启间隔：%d小时%d分钟", 间隔小时, 间隔分钟))
    end
    
    -- 只有在非出售时间段才返回大厅，避免中断出售任务
    local 当前时间 = os.date("*t")
    local 正在出售时段 = 在时间段内(当前时间.hour, 出售_开始时间, 出售_结束时间)
    if not 正在出售时段 and GameScript.modules.工具函数 and GameScript.modules.工具函数.尝试返回大厅 then
        GameScript.modules.工具函数.尝试返回大厅("制造检查完成")
    else
        显示信息("当前在出售时间段，不返回大厅，继续执行出售任务")
    end
end

-- 执行扫货抢购
local function 执行扫货抢购(是否挂机模式, 扫货价格)
    local 开始时间 = os.clock() * 1000
    Logger.info("扫货抢购", string.format("开始执行扫货抢购 - 挂机模式:%s, 价格:%d", tostring(是否挂机模式), 扫货价格))
    
    if not GameScript.modules.扫货抢购模块 then
        Logger.error("扫货抢购", "扫货抢购模块未加载")
        return false
    end
    
    -- 【修改】扫货时间检查，仅在挂机模式下生效
    local function 检查扫货时间()
        -- 【新逻辑】如果不是挂机模式，则始终允许执行，跳过时间检查。
        if not 是否挂机模式 then
            return true
        end

        local 当前时间 = os.date("*t")
        local 当前小时 = 当前时间.hour
        if not 在时间段内(当前小时, 0, 6) then
            -- 在挂机模式下，如果时间不符，则提示并停止
            显示信息("⚠️ 当前时间已过扫货时段(0-6点)，停止扫货任务")
            Logger.warn("扫货抢购", string.format("时间检查失败：当前时间%d点不在0-6点扫货时段内", 当前小时))
            return false
        end
        return true
    end
    
    -- 扫货开始前检查
    if not 检查扫货时间() then
        return false
    end
    
    -- 设置全局时间检查函数，供扫货模块调用
    -- 【修改】只有挂机模式才设置时间限制
    if 是否挂机模式 then
        _G.扫货时间检查 = 检查扫货时间
        _G.扫货时间限制 = true -- 启用时间限制
        Logger.debug("扫货抢购", "已为挂机模式启用扫货时间限制 (0-6点)")
    else
        _G.扫货时间检查 = nil
        _G.扫货时间限制 = false -- 禁用时间限制
        Logger.debug("扫货抢购", "单独扫货模式，已禁用时间限制")
    end
    
    -- 记录系统状态快照
    Logger.systemSnapshot("扫货抢购")
    
    if 是否挂机模式 then
        Logger.info("扫货抢购", "全自动模式：执行界面预处理和状态重置")
        
        -- 1. 强制返回大厅
        if GameScript.modules.工具函数 and GameScript.modules.工具函数.尝试返回大厅 then
            Logger.debug("扫货抢购", "强制返回大厅，确保干净的起始状态")
            GameScript.modules.工具函数.尝试返回大厅("全自动扫货预处理")
            sleep(2000)
        end
        
        -- 2. 清理弹窗状态
        local 清理状态, _ = pcall(function()
            if type(GameScript.modules.扫货抢购模块) == "table" and GameScript.modules.扫货抢购模块.清理弹窗 then
                Logger.debug("扫货抢购", "调用扫货模块的清理函数")
                扫货抢购模块.清理弹窗()
            else
                Logger.debug("扫货抢购", "使用通用清理方法")
                tap(91, 31)
                sleep(1000)
            end
        end)
        
        if not 清理状态 then
            Logger.warn("扫货抢购", "弹窗清理过程中出现问题")
        end
        
        -- 3. 设置全局标志
        _G.全自动挂机模式 = true
        _G.跳过复杂界面检测 = true
        _G.使用简化导航 = true
        Logger.debug("扫货抢购", "全自动模式预处理完成，设置全局标志")
    else
        Logger.info("扫货抢购", "单独扫货模式，使用标准逻辑")
        _G.全自动挂机模式 = false
        _G.跳过复杂界面检测 = false
        _G.使用简化导航 = false
    end
    
    _G.扫货输入价格 = 扫货价格
    _G.单独扫货模式 = not 是否挂机模式
    
    -- 【修复】使用统一错误处理执行扫货逻辑
    local 状态, 结果
    if type(GameScript.modules.扫货抢购模块) == "function" then
        Logger.debug("扫货抢购", "调用扫货抢购模块（函数类型）")
        状态, 结果 = GameScript.ErrorHandler.safeCall(
            GameScript.modules.扫货抢购模块,
            GameScript.ErrorHandler.ErrorType.UI,
            "扫货抢购执行",
            是否挂机模式
        )
    elseif type(GameScript.modules.扫货抢购模块) == "table" and type(GameScript.modules.扫货抢购模块.main) == "function" then
        Logger.debug("扫货抢购", "调用扫货抢购模块（表.main类型）")
        状态, 结果 = GameScript.ErrorHandler.safeCall(
            GameScript.modules.扫货抢购模块.main,
            GameScript.ErrorHandler.ErrorType.UI,
            "扫货抢购执行",
            是否挂机模式
        )
    else
        Logger.error("扫货抢购", "扫货抢购模块格式不正确")
        return false
    end
    
    -- 清理临时全局变量
    local function 清理临时全局变量()
        Logger.debug("扫货抢购", "开始清理临时全局变量")
        _G.全自动挂机模式 = nil
        _G.跳过复杂界面检测 = nil
        _G.使用简化导航 = nil
        _G.扫货时间限制 = nil
        _G.扫货时间检查 = nil -- 【新增】清理时间检查函数
        _G.单独扫货模式 = nil
        
        -- 清理OCR缓存
        if _G.特勤处制造 and _G.特勤处制造.清理OCR缓存 then
            _G.特勤处制造.清理OCR缓存()
            Logger.debug("扫货抢购", "OCR缓存已清理")
        end
        Logger.info("扫货抢购", "临时全局变量和缓存清理完成")
    end
    
    清理临时全局变量()
    
    -- 记录执行结果和性能
    local 执行时间 = (os.clock() * 1000) - 开始时间
    if not 状态 then
        Logger.error("扫货抢购", string.format("扫货抢购执行失败: %s", tostring(结果)), {
            duration_ms = 执行时间,
            mode = 是否挂机模式 and "挂机" or "单独",
            price = 扫货价格
        })
        return false
    else
        Logger.performance("扫货抢购", "扫货抢购完成", 执行时间, {
            success = true,
            mode = 是否挂机模式 and "挂机" or "单独",
            price = 扫货价格,
            result = 结果
        })
    end
    
    return true
end

-- 显示任务状态
local function 显示任务状态()
	local 当前时间戳 = os.time()
	local 上次状态计算时间 = 安全获取时间戳(State.上次状态计算时间, 当前时间戳 - 10)
	
    -- 不使用缓存，每次都重新计算状态信息，确保时间是最新的
    -- 但我们仍然保留状态计算的逻辑，用于控制刷新频率

	local 状态信息 = {"🔄 ══════ 挂机状态 ══════", "", "--- 任务时间段 ---"}
    
    -- 每次都获取最新的系统时间，确保时间显示是实时的
    local 当前时间 = os.date("*t")
	local 当前小时, 当前分钟 = 当前时间.hour, 当前时间.min
    local 距离分钟 = 60 - 当前分钟
	if 距离分钟 == 60 then 距离分钟 = 0 end
    
	-- 安全的时间计算
	local 出售_开始时间_safe = tonumber(出售_开始时间) or 13
	local 出售_结束时间_safe = tonumber(出售_结束时间) or 19
	
	-- 扫货时间计算
    local 距扫货时 = 0
	if 当前小时 >= 6 then
		距扫货时 = 24 - 当前小时
	end
	if 距离分钟 > 0 and 距扫货时 > 0 then 距扫货时 = 距扫货时 - 1 end

	-- 出售时间计算
    local 距出售时 = 0
	if not 在时间段内(当前小时, 出售_开始时间_safe, 出售_结束时间_safe) then
    	if 当前小时 < 出售_开始时间_safe then
        	距出售时 = 出售_开始时间_safe - 当前小时
		else
        	距出售时 = 24 - 当前小时 + 出售_开始时间_safe
    	end
		if 距离分钟 > 0 and 距出售时 > 0 then 距出售时 = 距出售时 - 1 end
	end
				
	local 正在扫货时段 = 在时间段内(当前小时, 0, 6)
	local 正在出售时段 = 在时间段内(当前小时, 出售_开始时间_safe, 出售_结束时间_safe)
				
    -- 任务时间段显示
	if 正在扫货时段 then
        table.insert(状态信息, "⚠️ 当前时段：扫货时间（0-6点）- 执行中，无状态显示")
    else
        table.insert(状态信息, string.format("🕒 扫货时间：⏰ 还有%d小时%d分钟（0-6点）", 距扫货时, 距离分钟))
    end
				
	if 正在出售时段 then
		if State.所有子弹已售罄 then
            table.insert(状态信息, "✅ 出售时间：已完成，当前挂机中")
    	else
            table.insert(状态信息, "⚠️ 当前时段：出售时间 - 执行中，无状态显示")
        end
	else
        table.insert(状态信息, string.format("🕒 出售时间：⏰ 还有%d小时%d分钟（%d-%d点）", 距出售时, 距离分钟, 出售_开始时间_safe, 出售_结束时间_safe))
    end
				
    -- 特勤处状态显示（只在挂机时且启用特勤处功能时显示）
    if 特勤处制造_功能开关 then
        table.insert(状态信息, "")
        table.insert(状态信息, "--- 特勤处状态 ---")
        
		if State.工作台都在制造 then
            table.insert(状态信息, "🏭 设备状态：✅ 全部在制造中")
			if State.下次制造检查时间戳 > 0 then
				local 剩余时间 = State.下次制造检查时间戳 - os.time()
                if 剩余时间 > 0 then
                    local 剩余小时 = math.floor(剩余时间 / 3600)
                    local 剩余分钟 = math.floor((剩余时间 % 3600) / 60)
                    table.insert(状态信息, string.format("🕒 下次检查：⏰ 还有%d小时%d分钟", 剩余小时, 剩余分钟))
                else
                    table.insert(状态信息, "🕒 下次检查：✅ 时间已到，准备检查")
                end
            end
        else
			table.insert(状态信息, string.format("🏭 设备状态：⚠️ %d个制造中, %d个空闲", State.当前制造中数量, State.当前空闲数量))
			if State.上次特勤处全面检查时间 > 0 then
				local 剩余时间 = (State.上次特勤处全面检查时间 + (3 * 60)) - os.time()
                if 剩余时间 > 0 then
                    local 剩余分钟 = math.floor(剩余时间 / 60)
                    local 剩余秒 = 剩余时间 % 60
                    table.insert(状态信息, string.format("🕒 下次检查：⏳ 约 %d分%d秒 后", 剩余分钟, 剩余秒))
                else
                    table.insert(状态信息, "🕒 下次检查：✅ 时间已到，等待触发")
                end
			else
				table.insert(状态信息, "🕒 下次检查：⏳ 即将触发首次检查...")
            end
        end
    end
	
	-- 定时重启状态显示（修复nil比较错误）
	if State.启用定时重启 then
		table.insert(状态信息, "")
		table.insert(状态信息, "--- 🔄 定时重启 ---")
		
		if State.重启进行中 then
			local 重启状态锁定时间 = 安全获取时间戳(State.重启状态锁定时间, 当前时间戳)
			local 重启持续时间 = 当前时间戳 - 重启状态锁定时间
			table.insert(状态信息, string.format("🔄 重启状态：🔄 重启进行中 (%d秒)", 重启持续时间))
		else
			local 上次重启完成时间 = 安全获取时间戳(State.上次重启完成时间, 当前时间戳)
			local 定时重启间隔 = tonumber(State.定时重启间隔) or (4 * 60 * 60) -- 【修复】移除此处的重启间隔更新调用
			local 距离下次重启秒数 = (上次重启完成时间 + 定时重启间隔) - 当前时间戳
			
			if 距离下次重启秒数 > 0 then
				local 剩余小时 = math.floor(距离下次重启秒数 / 3600)
				local 剩余分钟 = math.floor((距离下次重启秒数 % 3600) / 60)
				table.insert(状态信息, string.format("⏰ 距离重启：还有 %d小时%d分钟", 剩余小时, 剩余分钟))
			else
				table.insert(状态信息, "🚨 重启状态：⚠️ 时间已到，准备重启")
			end
		end
		
		-- 显示重启统计和总运行时间
		local 脚本启动时间 = 安全获取时间戳(State.脚本启动时间, 当前时间戳)
		local 重启计数 = tonumber(State.重启计数) or 0
		local 总运行时间 = 当前时间戳 - 脚本启动时间
		local 总运行小时 = math.floor(总运行时间 / 3600)
		local 总运行分钟 = math.floor((总运行时间 % 3600) / 60)
		table.insert(状态信息, string.format("📊 运行统计：%d小时%d分钟 (已重启%d次)", 总运行小时, 总运行分钟, 重启计数))
	end
	
	-- 当前挂机状态
	table.insert(状态信息, "")
	table.insert(状态信息, "--- 当前状态 ---")
	
	-- 根据当前时间段和任务状态，显示具体的状态描述
	local 当前时间_status = os.date("*t")
	local 当前小时_status = 当前时间_status.hour
	local 正在扫货时段_status = 在时间段内(当前小时_status, 0, 6)
	local 正在出售时段_status = 在时间段内(当前小时_status, 出售_开始时间_safe, 出售_结束时间_safe)
	
	local 状态描述 = ""
	
	if 正在扫货时段_status then
		状态描述 = "🔄 扫货时间段 - 执行扫货中"
	elseif 正在出售时段_status then
		if State.所有子弹已售罄 then
			状态描述 = "✅ 出售已完成，等待下个时间段"
		else
			状态描述 = "🔄 出售时间段 - 执行出售中"
		end
	else
		-- 非交易时间段的描述
		if State.上次执行的任务 == "扫货0-6" or State.上次执行的任务 == "抢购" then
			状态描述 = "✅ 扫货已完成，等待出售时间"
		elseif State.上次执行的任务 == "出售完成" then
			状态描述 = "✅ 出售已完成，等待扫货时间"
		elseif State.上次执行的任务 == "资金不足" then
			状态描述 = "⚠️ 资金不足，等待出售时间"
		elseif State.上次执行的任务 == "智能出售完成" then
			状态描述 = "✅ 智能出售完成，继续特勤处监控"
		else
			-- 默认挂机状态
			if 特勤处制造_功能开关 then
				状态描述 = "🏠 全功能挂机中，监控特勤处"
			else
				-- 判断扫货和出售哪个时间更近
				local 扫货剩余总分钟 = 距扫货时 * 60 + 距离分钟
				local 出售剩余总分钟 = 距出售时 * 60 + 距离分钟

				if 扫货剩余总分钟 < 出售剩余总分钟 then
					状态描述 = "🏠 正在等待扫货时间"
				else
					状态描述 = "🏠 正在等待出售时间"
				end
			end
		end
	end
	
	table.insert(状态信息, "📊 当前状态：" .. (State.当前动作描述 or 状态描述 or "状态未知"))
	table.insert(状态信息, "🕐 当前时间：" .. os.date("%H:%M:%S"))
    
    table.insert(状态信息, "════════════════")
				
    -- 缓存结果
    -- 每次都使用最新的时间戳
    State.缓存状态信息 = table.concat(状态信息, "\n")
    State.上次状态计算时间 = os.time()
    
    -- 显示状态信息
    -- 记录显示时间，用于防止频繁刷新
    State.上次状态显示时间 = os.time()
    toast(State.缓存状态信息)
    print(State.缓存状态信息)
end

-- 【新增】统一的状态更新和显示函数
local function 更新并显示状态面板(新动作描述)
    if 新动作描述 then
        State.当前动作描述 = 新动作描述
    end
    
    -- 无论是否有新动作，都强制清除缓存，确保时间戳等信息是最新的
    State.缓存状态信息 = nil
    State.上次状态计算时间 = 0
    
    -- 防止重复调用导致的状态面板闪烁
    local 当前时间戳 = os.time()
    local 上次调用时间 = State.上次状态面板调用时间 or 0
    if 当前时间戳 - 上次调用时间 < 3 and 新动作描述 == nil then
        -- 如果是短时间内的无消息更新，跳过显示
        return
    end
    
    State.上次状态面板调用时间 = 当前时间戳
    显示任务状态()
end

-- 检查是否需要定时重启
local function 检查是否需要定时重启()
	if not State.启用定时重启 then
		return false, "定时重启功能已禁用"
	end
	
	local 当前时间戳 = os.time()
	local 当前时间 = os.date("*t")
	local 当前小时 = 当前时间.hour
	
	-- 检查是否在交易时间段
	local 正在扫货时段 = 在时间段内(当前小时, 0, 6)
	local 正在出售时段 = 在时间段内(当前小时, 出售_开始时间, 出售_结束时间)
	
    -- 【关键修改】全自动模式的重启逻辑
    if 自动挂机 then
        -- 在交易时间段禁止重启，确保任务正常执行
        if 正在扫货时段 then
            return false, "全自动模式：当前在扫货时间段（0-6点），禁止重启"
        elseif 正在出售时段 then
            return false, "全自动模式：当前在出售时间段，禁止重启"
        end
        -- 非交易时间段允许重启（主要用于故障恢复）
    end
	
	-- 如果勾选了仅特勤处制造，在交易时间段不重启
	if 特勤处制造_功能开关 and not 自动挂机 then
		if 正在扫货时段 then
			return false, "仅特勤处模式：当前在扫货时间段（0-6点），跳过重启"
		elseif 正在出售时段 then
			return false, "仅特勤处模式：当前在出售时间段，跳过重启"
		end
	end
	
	-- 如果勾选了其他单功能模式，在交易时间段不重启
	if 扫货功能 or 房卡功能 or 出售子弹 or 五级蛋功能 then
		if 正在扫货时段 then
			return false, "单功能模式：当前在扫货时间段（0-6点），跳过重启"
		elseif 正在出售时段 then
			return false, "单功能模式：当前在出售时间段，跳过重启"
		end
	end
	
	-- 安全获取时间戳，避免nil比较
	local 上次重启检查时间 = 安全获取时间戳(State.上次重启检查时间, 0)
	local 重启检查冷却时间 = tonumber(State.重启检查冷却时间) or 300 -- 5分钟冷却时间
	
	-- 防抖动检查：避免频繁检查
	if 当前时间戳 - 上次重启检查时间 < 重启检查冷却时间 then
		return false, string.format("重启检查冷却中，距下次检查还有%d秒", 
									重启检查冷却时间 - (当前时间戳 - 上次重启检查时间))
	end
	State.上次重启检查时间 = 当前时间戳
	
	-- 检查重启状态锁
	if State.重启进行中 then
		local 重启状态锁定时间 = 安全获取时间戳(State.重启状态锁定时间, 0)
		local 重启持续时间 = 当前时间戳 - 重启状态锁定时间
		local 最大持续时间 = tonumber(State.重启状态最大持续时间) or 60
		
		if 重启持续时间 > 最大持续时间 then
			显示信息("⚠️ 重启状态持续过长，强制解锁")
			State.重启进行中 = false
			State.重启状态锁定时间 = 0
		else
			return false, string.format("重启进行中，已持续%d秒", 重启持续时间)
		end
	end
	
	-- 动态更新重启间隔
	-- local 当前间隔 = 更新重启间隔() -- 【修复】移除这里的频繁调用
	
	-- 安全获取重启相关时间戳
	local 上次重启完成时间 = 安全获取时间戳(State.上次重启完成时间, 0)
	local 定时重启间隔 = tonumber(State.定时重启间隔) or (4 * 60 * 60) -- 使用现有间隔
	
	-- 检查是否到达重启时间
	local 距离上次重启时间 = 当前时间戳 - 上次重启完成时间
	
	-- 系统时间异常检查
	if 距离上次重启时间 < 0 then
		显示信息("⚠️ 检测到系统时间异常，重置重启时间基准")
		State.上次重启完成时间 = 当前时间戳
		return false, "系统时间异常，已重置基准时间"
	end
	
	-- 检查是否达到重启间隔
	if 距离上次重启时间 >= 定时重启间隔 then
		local 脚本启动时间 = 安全获取时间戳(State.脚本启动时间, 当前时间戳)
		local 总运行时间 = 当前时间戳 - 脚本启动时间
		local 运行小时 = math.floor(总运行时间 / 3600)
		local 运行分钟 = math.floor((总运行时间 % 3600) / 60)
		local 重启计数 = tonumber(State.重启计数) or 0
		
		return true, string.format("已运行%d小时%d分钟，执行智能重启（第%d次）", 
								  运行小时, 运行分钟, 重启计数 + 1)
	end
	
	return false, ""
end

-- 全局错误处理器
local function 全局错误处理(错误信息, 模块名)
    -- 详细记录错误到日志
    if Logger then
        Logger.error(模块名 or "Unknown", string.format("全局错误处理: %s", tostring(错误信息)), {
            stack_trace = debug.traceback(),
            memory_usage = math.floor(collectgarbage("count")),
            state_snapshot = {
                last_task = State.上次执行的任务,
                sold_out = State.所有子弹已售罄,
                manufacturing = State.工作台都在制造
            }
        })
    end
    
    显示信息(string.format("模块[%s]发生严重错误: %s", 模块名 or "未知", tostring(错误信息)))
    
    -- 尝试恢复到安全状态
    if GameScript.modules.工具函数 and GameScript.modules.工具函数.尝试返回大厅 then
        Logger.info("ErrorRecovery", "尝试返回大厅恢复状态")
        GameScript.modules.工具函数.尝试返回大厅("错误恢复")
        sleep(2000)
    end
    
    -- 重置相关状态
    if State then
        更新状态("重启进行中", false, "错误恢复")
        更新状态("重启状态锁定时间", 0, "错误恢复")
        更新状态("允许状态显示", true, "错误恢复")
        更新状态("下次状态更新时间", os.time() + 5, "错误恢复")
    end
    
    Logger.info("ErrorRecovery", "错误恢复完成，等待5秒后重试")
    sleep(5000)
    return false
end

-- 执行定时重启
local function 执行定时重启(重启原因)
	-- 设置重启锁
	if State.重启进行中 then
		显示信息("⚠️ 重启流程已在进行中，忽略重复请求")
		return
	end
	
	-- 立即设置重启状态，防止重复触发
	更新状态("重启进行中", true, "启动重启流程")
	更新状态("重启状态锁定时间", os.time(), "启动重启流程")
	更新状态("重启计数", State.重启计数 + 1, "启动重启流程")
	
	显示信息("🔄 ═══════ 定时重启流程开始 ═══════")
	显示信息("重启原因：" .. 重启原因)
	显示信息(string.format("这是第 %d 次定时重启", State.重启计数))
	显示信息(string.format("重启开始时间：%s", os.date("%H:%M:%S", State.重启状态锁定时间)))
	
	-- 暂停状态显示
	暂停状态显示()
	
	-- 执行游戏重启
	if type(GameScript.modules.自动重启游戏) == "function" then
		显示信息("正在执行游戏重启，清理缓存...")
		local 重启状态, 重启结果 = pcall(GameScript.modules.自动重启游戏)
		if 重启状态 then
			显示信息("✅ 游戏重启成功！缓存已清理")
		else
			显示信息("⚠️ 游戏重启过程中出现问题：" .. tostring(重启结果))
		end
	else
		显示信息("❌ 警告：自动重启游戏函数不可用，跳过重启")
	end
	
	-- 重启后的状态重置
	显示信息("重启完成，重新初始化脚本状态...")
	更新状态("初始检查完成", false, "重启后重置")
	更新状态("上次特勤处全面检查时间", 0, "重启后重置")
	更新状态("交易行查找连续失败次数", 0, "重启后重置")
	更新状态("下次制造检查时间戳", 0, "重启后重置")
	更新状态("工作台都在制造", false, "重启后重置")
	
	-- 【新增】重置特勤处检查锁，确保重启后可以立即检查
	if State.特勤处检查锁 then
	    更新状态("特勤处检查锁", {
	        正在检查中 = false,
	        上次检查完成时间 = 0,
	        最小检查间隔 = 180
	    }, "重启后重置")
	end
	
	-- 【关键修复】注释掉这行错误的状态重置，它不应该干涉节能模式的状态
	-- 更新状态("isGameClosed", false, "重启后重置")
	
	-- 智能重置售罄状态
	local 当前时间 = os.date("*t")
	local 当前小时 = 当前时间.hour
	local 正在出售时段 = 在时间段内(当前小时, 出售_开始时间, 出售_结束时间)
	
	if 正在出售时段 and State.所有子弹已售罄 then
		更新状态("所有子弹已售罄", true, "重启后保持售罄")
		更新状态("上次执行的任务", "出售完成", "重启后保持售罄")
		显示信息("💡 保持售罄状态（当前在出售时段）")
	else
		更新状态("所有子弹已售罄", false, "重启后重置售罄")
		更新状态("上次执行的任务", "", "重启后重置任务")
		显示信息("💡 重置售罄状态，重新检测库存")
	end
	
	-- 更新状态更新时间
	更新状态("下次状态更新时间", os.time() + 5, "重启后重置")
	
	-- 等待游戏稳定
	显示信息("等待游戏环境稳定...")
	sleep(3000)
	
	-- 恢复状态显示并解锁
	恢复状态显示()
	
	-- 只在重启完全完成后再更新时间基准
	更新状态("上次重启完成时间", os.time(), "重启流程完成")
	更新状态("重启进行中", false, "重启流程完成")
	更新状态("重启状态锁定时间", 0, "重启流程完成")
	
	-- 重新计算重启间隔
	local 新间隔 = 更新重启间隔()
	local 间隔小时 = math.floor(新间隔 / 3600)
	local 间隔分钟 = math.floor((新间隔 % 3600) / 60)
	
	显示信息("🔄 ═══════ 定时重启流程完成 ═══════")
	显示信息(string.format("下次重启时间大约在：%s (间隔%d小时%d分钟)", 
						  os.date("%H:%M:%S", State.上次重启完成时间 + State.定时重启间隔),
						  间隔小时, 间隔分钟))
	显示信息("脚本将继续正常运行...")
	
	-- 立即更新状态显示
	sleep(1000)
	显示任务状态()
end

-- 处理交易任务
local function 处理交易任务(当前小时)
    -- 【新增】强制时间检查 - 如果当前任务与时间段不符，立即停止
    local 正在扫货时段 = 在时间段内(当前小时, 0, 6)
    local 正在出售时段 = 在时间段内(当前小时, 出售_开始时间, 出售_结束时间)
    
    -- 【关键修复】如果当前执行扫货任务但已过扫货时间，强制停止
    if State.上次执行的任务 == "扫货0-6" and not 正在扫货时段 then
        更新状态("上次执行的任务", "扫货结束", "强制停止扫货")
        恢复状态显示()
        更新并显示状态面板("⚠️ 已过扫货时间(6点)，强制停止扫货任务")
        
        -- 确保返回大厅，停止扫货相关操作
        if GameScript.modules.工具函数 and GameScript.modules.工具函数.尝试返回大厅 then
            GameScript.modules.工具函数.尝试返回大厅("强制停止扫货")
        end
        
        return "SCANNING_STOPPED" -- 返回停止状态
    end
    
    -- 【关键修复】如果当前执行出售任务但已过出售时间，强制停止
    if State.上次执行的任务 == "出售" and not 正在出售时段 then
        更新状态("上次执行的任务", "出售结束", "强制停止出售")
        恢复状态显示()
        更新并显示状态面板("⚠️ 已过出售时间，强制停止出售任务")
        return "SELLING_STOPPED" -- 返回停止状态
    end

    -- 优先处理出售时间
if 在时间段内(当前小时, 出售_开始时间, 出售_结束时间) then
    更新并显示状态面板("🕐 当前处于出售时间段 (" .. 出售_开始时间 .. "-" .. 出售_结束时间 .. "点)")
    
    -- 【修复1】增加出售前的状态检查
    显示信息("📊 出售前状态检查:", false)
    显示信息("  - 售罄状态: " .. tostring(State.所有子弹已售罄), false)
    显示信息("  - 子弹类型: " .. tostring(_G.多选框_出售子弹_type), false)
    显示信息("  - 出售价格: " .. tostring(输入框_出售价格), false)
    显示信息("  - 上次任务: " .. tostring(State.上次执行的任务), false)
    
    -- 【修复2】优化游戏启动检查
    local 游戏启动成功 = false
    local 启动尝试次数 = 0
    local 最大启动尝试 = 2  -- 限制启动尝试次数
    
    while not 游戏启动成功 and 启动尝试次数 < 最大启动尝试 do
        启动尝试次数 = 启动尝试次数 + 1
        更新并显示状态面板(string.format("🎮 第%d次尝试确保游戏运行...", 启动尝试次数))
        
        游戏启动成功 = 检查并启动游戏()
        
        if not 游戏启动成功 and 启动尝试次数 < 最大启动尝试 then
            更新并显示状态面板("⚠️ 游戏启动检查失败，5秒后重试...")
            sleep(5000)
        end
    end
    
    if not 游戏启动成功 then
        更新并显示状态面板("❌ 游戏启动失败，跳过本轮出售")
        return "GAME_START_FAILED"
    end
    
    更新并显示状态面板("✅ 游戏状态确认正常")

	if not State.所有子弹已售罄 then
		-- 进入出售状态，暂停状态显示
		if State.上次执行的任务 ~= "出售" then
			暂停状态显示()
			
			-- 【修复3】简化特勤处检查逻辑，避免干扰出售
			if 特勤处制造_功能开关 then
				更新并显示状态面板("⚙️ 出售前快速检查特勤处状态...")
				-- 只在真正需要时才执行特勤处检查，避免干扰出售
				local should_check, check_reason = 检查是否需要执行特勤处检查()
				if should_check and string.find(check_reason, "首次") then
					更新并显示状态面板("⚙️ 执行首次特勤处检查...")
					执行特勤处制造检查(check_reason)
					更新并显示状态面板("✅ 特勤处检查完成，开始出售任务")
				else
					更新并显示状态面板("⏭️ 跳过特勤处检查，直接开始出售")
				end
			end

			更新并显示状态面板("💰 正式进入出售时间，开始执行出售任务...")
			
			更新状态("所有子弹已售罄", false, "进入出售时间段")
			更新状态("上次执行的任务", "出售", "进入出售时间段")
		else
			更新并显示状态面板("💰 继续执行出售任务...")
		end
		
		-- 【修复4】增强出售任务执行逻辑
		更新并显示状态面板("🔄 开始执行出售任务...")
		
		-- 验证出售模块完整性
		if not GameScript.modules.出售子弹模块 then
			更新并显示状态面板("❌ 致命错误：出售子弹模块未加载！")
			更新状态("上次执行的任务", "出售模块缺失", "模块检查失败")
			恢复状态显示()
			return "SELLING_MODULE_MISSING"
		end
		
		local funcs_map = {
			["0"] = "自动出售9_19", 
			["1"] = "自动出售57_28", 
			["2"] = "自动出售45ACP", 
			["3"] = "自动出售12_Gauge", 
			["4"] = "自动出售300_BLK"
		}
		local func_to_call = funcs_map[_G.多选框_出售子弹_type]
		
		-- 详细的函数检查
		if not func_to_call then
			更新并显示状态面板("❌ 无效的子弹类型设置：" .. tostring(_G.多选框_出售子弹_type))
			return "SELLING_INVALID_TYPE"
		end
		
		if not GameScript.modules.出售子弹模块[func_to_call] then
			更新并显示状态面板("❌ 出售函数不存在：" .. func_to_call)
			显示信息("📝 可用的出售函数：", false)
			for key, _ in pairs(GameScript.modules.出售子弹模块) do
				显示信息("  - " .. tostring(key), false)
			end
			return "SELLING_FUNCTION_MISSING"
		end
		
		-- 执行出售任务
		更新并显示状态面板("💰 调用出售函数：" .. func_to_call .. "，价格：" .. 输入框_出售价格 .. "元")
		
		local 出售开始时间 = os.time()
		local ok, sell_result = pcall(GameScript.modules.出售子弹模块[func_to_call], 输入框_出售价格, State)
		local 出售耗时 = os.time() - 出售开始时间
		
		显示信息(string.format("⏱️ 出售任务执行耗时：%d秒", 出售耗时), false)
		
		if ok and sell_result then
			local 状态码 = sell_result.状态 or sell_result
			显示信息("📊 出售任务返回状态码: " .. tostring(状态码), false)
			
			-- 检查售罄状态
			if 状态码 == "SOLD_OUT" or 状态码 == "售罄" or tostring(状态码):find("售罄") then
				更新状态("所有子弹已售罄", true, "检测到售罄状态")
				更新并显示状态面板("✅ 检测到子弹已售罄，出售任务完成")
				
				-- 售罄后自动领取邮件
				更新并显示状态面板("📧 子弹已售罄，自动领取邮件...")
				local mail_ok, mail_module = pcall(require, "自动领取邮件")
				if mail_ok and type(mail_module) == "function" then
					local mail_status, mail_result = pcall(mail_module)
					if mail_status then
						更新并显示状态面板("✅ 售罄后邮件领取完成")
					else
						更新并显示状态面板("⚠️ 售罄后邮件领取失败：" .. tostring(mail_result))
					end
				else
					更新并显示状态面板("⚠️ 自动领取邮件模块加载失败")
				end
				
				-- 确保返回大厅
				if GameScript.modules.工具函数 and GameScript.modules.工具函数.尝试返回大厅 then
					GameScript.modules.工具函数.尝试返回大厅("售罄后邮件领取完成")
					sleep(2000)
				end
				
				-- 出售完成，切换到挂机状态（恢复状态显示）
				更新状态("上次执行的任务", "出售完成", "子弹售罄")
				恢复状态显示()
				更新并显示状态面板("✅ 出售任务完成，切换到挂机状态")
				return "SELLING_COMPLETED"
				
			elseif 状态码 == "SUCCESS" or 状态码 == "成功" then
				更新并显示状态面板("✅ 本轮出售成功，继续下一轮出售...")
				State.交易行查找连续失败次数 = 0 -- 重置失败计数
			else
				更新并显示状态面板("⚠️ 出售状态未知: " .. tostring(状态码))
				State.交易行查找连续失败次数 = (State.交易行查找连续失败次数 or 0) + 1
			end
		elseif not ok then
			更新并显示状态面板("❌ 出售任务执行发生错误: " .. tostring(sell_result))
			State.交易行查找连续失败次数 = (State.交易行查找连续失败次数 or 0) + 1
			
			-- 连续失败保护
			if State.交易行查找连续失败次数 >= 3 then
				更新并显示状态面板("❌ 出售连续失败3次，可能存在严重问题，暂停出售")
				更新状态("上次执行的任务", "出售失败", "连续失败保护")
				恢复状态显示()
				return "SELLING_REPEATED_FAILURE"
			end
		end
		
		return "SELLING_IN_PROGRESS"
	else
		-- 出售时间但已售罄
		if State.上次执行的任务 ~= "出售完成" then
			更新并显示状态面板("✅ 子弹已售罄，继续挂机状态，执行特勤处检查")
			更新状态("上次执行的任务", "出售完成", "出售时间但已售罄")
			State.交易行查找连续失败次数 = 0
			恢复状态显示()
		end
		
		-- 非挂机模式下，售罄后直接退出
		if not 自动挂机 then
			更新并显示状态面板("🔚 非挂机模式且已售罄，脚本退出")
			return "SOLD_OUT_EXIT"
		end
	end
    -- 其次处理扫货时间
    elseif 在时间段内(当前小时, 0, 6) then
        检查并启动游戏()
        if State.上次执行的任务 ~= "扫货0-6" then
            暂停状态显示()
            更新并显示状态面板("进入扫货时间（0-6点），开始执行扫货任务...")
            更新状态("上次执行的任务", "扫货0-6", "进入0-6点扫货时间")
        end
        
        -- 【新增】在扫货前检查时间，确保仍在扫货时段
        local 当前检查时间 = os.date("*t").hour
        if 在时间段内(当前检查时间, 0, 6) then
        pcall(执行扫货抢购, true, State.扫货输入价格)
        else
            更新并显示状态面板("⚠️ 检测到时间已过扫货时段，停止扫货")
            更新状态("上次执行的任务", "扫货超时结束", "时间检查")
            恢复状态显示()
            return "SCANNING_TIMEOUT"
        end
        
        -- 扫货后检查资金不足标志
        if _G.资金不足 then
            更新并显示状态面板("检测到资金不足，扫货任务已停止。等待出售时间。")
            更新状态("上次执行的任务", "资金不足", "扫货因资金不足停止")
            _G.资金不足 = false -- 重置标志
            恢复状态显示()
        end
        return "SCANNING_0_6"
        
    -- 最后处理挂机时间
    else
        -- 如果当前任务是 "出售" 或 "扫货"，但时间已经不符，强制切换到挂机
        if State.上次执行的任务 == "出售" or State.上次执行的任务 == "扫货0-6" then
            更新并显示状态面板("✅ 任务时间段已过，强制切换到挂机状态。")
            恢复状态显示()
        end

        if State.上次执行的任务 ~= "挂机" then
            if 特勤处制造_功能开关 then
                更新并显示状态面板("进入挂机时间，执行特勤处检查")
            else
                更新并显示状态面板("进入挂机时间，等待下一个交易时段")
                
                -- 【新增】仅交易挂机模式也进入节能模式
                local 距离上次重启 = os.time() - GameScript.safeGetNumber(State.上次重启完成时间, 0)
                -- 检查当前是否在交易时间段
                local 当前小时 = tonumber(os.date("%H"))
                local 出售_开始时间_safe = tonumber(出售_开始时间) or 13
                local 出售_结束时间_safe = tonumber(出售_结束时间) or 19
                local 在扫货时间段 = 在时间段内(当前小时, 0, 6)
                local 在出售时间段 = 在时间段内(当前小时, 出售_开始时间_safe, 出售_结束时间_safe)
                local 在交易时间段 = 在扫货时间段 or 在出售时间段
                
                -- 计算距离下次交易时间
                local 距离扫货时间_分钟 = 0
                if 当前小时 >= 6 then
                    距离扫货时间_分钟 = (24 - 当前小时) * 60
                elseif 当前小时 < 0 then
                    距离扫货时间_分钟 = 0 -- 已经在扫货时间段
                end
                
                -- 只有在非交易时间段且距离上次重启超过5分钟才关闭游戏
                if 自动挂机 and 距离上次重启 > (5 * 60) and not 在交易时间段 and 距离扫货时间_分钟 > 60 then
                    更新并显示状态面板(string.format("仅交易模式：非交易时段(当前%d点)，关闭游戏进入节能模式...", 当前小时))
                    pcall(function() stopApp("com.tencent.tmgp.dfm") end)
                    GameScript.setState("isGameClosed", true, "仅交易模式节能")
                    
                    -- 设置下次唤醒时间（根据最近的交易时间段）
                    local 当前时间 = os.date("*t")
                    local 当前小时 = 当前时间.hour
                    local 当前分钟 = 当前时间.min
                    
                    local 距离扫货时间 = 0
                    if 当前小时 >= 6 then
                        距离扫货时间 = (24 - 当前小时) * 3600 - 当前分钟 * 60
                    else
                        距离扫货时间 = 0 -- 已经在扫货时间段
                    end
                    
                    local 距离出售时间 = 0
                    if not 在时间段内(当前小时, 出售_开始时间, 出售_结束时间) then
                        if 当前小时 < 出售_开始时间 then
                            距离出售时间 = (出售_开始时间 - 当前小时) * 3600 - 当前分钟 * 60
                        else
                            距离出售时间 = (24 - 当前小时 + 出售_开始时间) * 3600 - 当前分钟 * 60
                        end
                    else
                        距离出售时间 = 0 -- 已经在出售时间段
                    end
                    
                    -- 取最近的交易时间作为唤醒时间
                    local 下次唤醒时间 = 0
                    if 距离扫货时间 > 0 and 距离出售时间 > 0 then
                        下次唤醒时间 = os.time() + math.min(距离扫货时间, 距离出售时间) - 300 -- 提前5分钟唤醒
                    elseif 距离扫货时间 > 0 then
                        下次唤醒时间 = os.time() + 距离扫货时间 - 300
                    elseif 距离出售时间 > 0 then
                        下次唤醒时间 = os.time() + 距离出售时间 - 300
                    else
                        下次唤醒时间 = os.time() + 3600 -- 默认1小时后唤醒
                    end
                    
                    GameScript.setState("下次任务重启时间", 下次唤醒时间, "仅交易模式下次唤醒")
                    更新并显示状态面板(string.format("⏰ 下次唤醒时间：%s", os.date("%H:%M:%S", 下次唤醒时间)))
                end
            end
            更新状态("上次执行的任务", "挂机", "进入通用挂机时间")
            恢复状态显示()
        end
        return "IDLE"
		
	end
end

-- =================== 核心修复函数 ===================
-- 安全的数值获取和比较函数
local function 安全获取数值(值, 默认值)
	默认值 = 默认值 or 0
	if 值 == nil then
		return 默认值
	end
	local 数值 = tonumber(值)
	if 数值 == nil then
		return 默认值
	end
	return 数值
end

-- 安全的时间戳比较函数
local function 安全时间比较(时间戳1, 时间戳2, 描述)
	描述 = 描述 or "时间比较"
	local 当前时间戳 = os.time()
	
	local 安全时间戳1 = 安全获取数值(时间戳1, 当前时间戳)
	local 安全时间戳2 = 安全获取数值(时间戳2, 当前时间戳)
	
	return 安全时间戳1 >= 安全时间戳2
end

-- =================== 修复State初始化 ===================
-- 确保State中的所有时间相关字段都有有效的初始值
local function 强制初始化State()
	if not _G.State then
		_G.State = {}
	end
	
	local 当前时间戳 = os.time()
	
	-- 强制设置所有可能为nil的时间字段
	_G.State.下次状态更新时间 = _G.State.下次状态更新时间 or 当前时间戳
	_G.State.下次制造检查时间戳 = _G.State.下次制造检查时间戳 or 0
	_G.State.上次特勤处全面检查时间 = _G.State.上次特勤处全面检查时间 or 0
	_G.State.上次重启完成时间 = _G.State.上次重启完成时间 or 当前时间戳
	_G.State.上次重启检查时间 = _G.State.上次重启检查时间 or 当前时间戳
	_G.State.重启状态锁定时间 = _G.State.重启状态锁定时间 or 0
	_G.State.上次状态计算时间 = _G.State.上次状态计算时间 or 0
	_G.State.脚本启动时间 = _G.State.脚本启动时间 or 当前时间戳
	
	-- 确保数值字段有效
	_G.State.重启计数 = 安全获取数值(_G.State.重启计数, 0)
	_G.State.定时重启间隔 = 安全获取数值(_G.State.定时重启间隔, 更新重启间隔())
	_G.State.重启检查冷却时间 = 安全获取数值(_G.State.重启检查冷却时间, 300) -- 5分钟冷却时间
	_G.State.重启状态最大持续时间 = 安全获取数值(_G.State.重启状态最大持续时间, 60)
end

-- =================== 修复版主循环关键部分 ===================
-- 修复版时间检查函数
local function 修复版时间检查()
	强制初始化State() -- 每次检查前都确保State有效
	
	local 当前时间戳 = os.time()
	local 当前时间 = os.date("*t")
	
	-- 使用安全比较检查状态更新时间
	local 需要更新状态 = 安全时间比较(当前时间戳, _G.State.下次状态更新时间, "状态更新检查")
	
	-- 使用安全比较检查制造检查时间
	local 需要制造检查 = false
	if _G.State.工作台都在制造 and _G.State.下次制造检查时间戳 > 0 then
		需要制造检查 = 安全时间比较(当前时间戳, _G.State.下次制造检查时间戳, "制造检查")
	else
		-- 如果不在全部制造，则使用周期检查
		local 距离上次检查时间 = 当前时间戳 - 安全获取数值(_G.State.上次特勤处全面检查时间, 0)
		if 距离上次检查时间 >= (3 * 60) then
			需要制造检查 = true
		end
	end
	
	-- 使用安全比较检查重启时间
	local 需要重启检查 = false
	if _G.State.启用定时重启 then
		local 上次检查间隔 = 当前时间戳 - 安全获取数值(_G.State.上次重启检查时间, 当前时间戳)
		需要重启检查 = 上次检查间隔 >= 安全获取数值(_G.State.重启检查冷却时间, 300) -- 5分钟冷却时间
	end
	
	return {
		需要更新状态 = 需要更新状态,
		需要制造检查 = 需要制造检查,
		需要重启检查 = 需要重启检查,
		当前时间戳 = 当前时间戳,
		当前小时 = 当前时间.hour
	}
end

-- =================== 各模式主循环函数定义 ===================

-- 【新增】更安全的节能模式唤醒逻辑
local function 安全唤醒游戏(原因)
    更新并显示状态面板("🔄 " .. 原因 .. "，从节能模式唤醒...")
    
    -- 1. 更新状态，表明我们正在尝试唤醒
    GameScript.setState("isGameClosed", false, 原因)
    
    -- 2. 直接调用专用的重启函数
    local 启动成功 = false
    if GameScript.modules.自动重启游戏 and type(GameScript.modules.自动重启游戏) == "function" then
        更新并显示状态面板("正在调用专用的游戏重启/唤醒流程...")
        local ok, result = pcall(GameScript.modules.自动重启游戏)
        
        if ok and result then
            启动成功 = true
            更新并显示状态面板("✅ 节能模式唤醒成功，游戏已启动")
        else
            更新并显示状态面板("❌ 专用的重启/唤醒流程执行失败。")
            Logger.error("EnergyMode", "自动重启游戏函数执行失败或返回false", {pcall_ok = ok, result = result})
        end
    else
        更新并显示状态面板("❌ 错误：自动重启游戏模块不可用，无法唤醒！")
        Logger.error("EnergyMode", "自动重启游戏模块未加载或不是函数")
    end

    -- 3. 根据最终结果返回并更新状态
    if not 启动成功 then
        -- 如果唤醒失败，恢复节能状态
        更新并显示状态面板("⚠️ 唤醒失败，恢复节能模式")
        GameScript.setState("isGameClosed", true, "唤醒失败，恢复节能模式")
        return false
    end
    
    return true
end

-- 使用增强版检查的全功能挂机模式
local function 执行全功能挂机模式_增强版()
    -- 内嵌辅助函数：检查是否需要执行特勤处检查
    local function 检查是否需要执行特勤处检查_增强版()
        local 当前时间戳 = os.time()
        local 下次检查时间戳 = GameScript.getState("下次制造检查时间戳", 0)
        
        -- 首次运行，需要执行初始检查
        if not GameScript.getState("首次检查已完成", false) then
            return true, "首次运行，执行初始检查..."
        end
        
        -- 如果已经设置了下次检查时间，且当前时间已经超过，则需要检查
        if 下次检查时间戳 > 0 and 当前时间戳 >= 下次检查时间戳 then
            return true, "达到预定检查时间"
        end
        
        return false, "不需要检查"
    end

    -- 主循环
    -- 添加状态面板定时刷新控制
    local 上次状态面板刷新时间 = 0
    local 状态面板刷新间隔 = 120 -- 2分钟刷新一次
    local 上次显示状态时间 = 0
    local 状态显示间隔 = 60 -- 60秒显示一次状态面板
    
    while true do
        local sleep_duration = 5000 -- 默认休眠5秒，减少CPU使用
        
        local 当前时间戳 = os.time()
        
        -- 定时刷新状态面板（每2分钟强制刷新一次）
        if 当前时间戳 - 上次状态面板刷新时间 >= 状态面板刷新间隔 then
            -- 强制刷新状态，清除缓存
            State.当前动作描述 = nil -- 清除当前动作，使用默认状态描述
            State.缓存状态信息 = nil -- 强制重新计算状态
            State.上次状态计算时间 = 0 -- 重置计算时间
            上次状态面板刷新时间 = 当前时间戳
            显示信息("定时刷新状态面板，更新时间...", false)
            
            -- 不在这里立即显示，避免重复显示
            -- 下一个显示检查会处理
        end
        
        -- 控制状态面板显示频率
        if 当前时间戳 - 上次显示状态时间 >= 状态显示间隔 then
            更新并显示状态面板(nil) -- 传入nil以刷新现有状态
            上次显示状态时间 = 当前时间戳
        end
        
        if GameScript.getState("isGameClosed", false) then
            -- [分支1: 游戏关闭 - 节能模式]
            sleep_duration = 5000 -- 节能模式下，轮询间隔长一些
            local 当前小时 = tonumber(os.date("%H", 当前时间戳))
            
            -- 1a. 检查是否需要执行任务重启
            local 任务重启时间 = GameScript.getState("下次任务重启时间", 0)
            if 任务重启时间 > 0 and 当前时间戳 >= 任务重启时间 then
                显示信息("⏰ 到达任务重启时间，执行重启...")
                自动重启游戏() -- 调用现有的重启模块
                GameScript.setState("下次任务重启时间", 0, "任务重启完成")
                GameScript.setState("isGameClosed", false, "任务重启后唤醒")
                goto continue -- 重启后立即进入下一轮循环，不再休眠
            end

            -- 1b. 检查是否需要因交易而唤醒
                        local 需要唤醒, 唤醒原因 = false, ""
            local 出售_开始时间_safe = tonumber(出售_开始时间) or 13
            local 出售_结束时间_safe = tonumber(出售_结束时间) or 19
            
            -- 【修改】同时支持全功能模式和仅交易模式的唤醒
            if 在时间段内(当前小时, 0, 6) then
                -- 扫货时间段唤醒条件
                if 扫货功能 or 自动挂机 then
                     需要唤醒, 唤醒原因 = true, "扫货时间段"
                end
            elseif 在时间段内(当前小时, 出售_开始时间_safe, 出售_结束时间_safe) then
                -- 出售时间段唤醒条件
                if 出售子弹 or 自动挂机 then
                     需要唤醒, 唤醒原因 = true, "出售时间段"
                end
            end
            
            -- 【新增】检查是否有设定的下次任务重启时间
            if not 需要唤醒 and 任务重启时间 > 0 and (任务重启时间 - 当前时间戳) < 300 then
                -- 如果距离预定的唤醒时间不足5分钟，提前唤醒
                需要唤醒, 唤醒原因 = true, "预定唤醒时间即将到达"
            end
            
            if 需要唤醒 then
                更新并显示状态面板(string.format("🔔 节能模式唤醒，原因: %s", 唤醒原因))
                if 安全唤醒游戏(唤醒原因) then
                    GameScript.setState("isGameClosed", false, "节能模式唤醒")
                else
                    更新并显示状态面板("⚠️ 唤醒失败，30秒后重试...")
                    sleep_duration = 30000 -- 唤醒失败，则30秒后重试
                end
            else
                -- 1c. 无需唤醒，打印心跳日志
                if 当前时间戳 >= (GameScript.getState("下次心跳时间", 0)) then
                    local 下次任务时间 = GameScript.getState("下次任务重启时间", 0)
                    if 下次任务时间 > 0 then
                        local 距离下次任务 = 下次任务时间 - 当前时间戳
                        local 距离小时 = math.floor(距离下次任务 / 3600)
                        local 距离分钟 = math.floor((距离下次任务 % 3600) / 60)
                        更新并显示状态面板(string.format("💤 节能休眠中... 下次任务还剩 %d小时%d分钟", 距离小时, 距离分钟))
                    else
                        更新并显示状态面板("💤 节能休眠中... 等待下次任务")
                    end
                    GameScript.setState("下次心跳时间", 当前时间戳 + 300, "节能心跳")
                end
            end
        else
            -- [分支2: 游戏运行中]
            local 当前小时 = tonumber(os.date("%H", 当前时间戳))
            
            -- 2a. 优先处理交易任务
            local task_result = 处理交易任务(当前小时)
            if task_result == "SCANNING_0_6" or task_result == "SELLING_IN_PROGRESS" then
                Logger.debug("MainLoop", "当前处于交易执行状态，等待任务完成...")
                -- 交易时，继续循环，不做其他事
            elseif task_result == "SELLING_COMPLETED" and 自动挂机 and 特勤处制造_功能开关 then
                -- 出售完成后，立即执行特勤处检查
                Logger.info("MainLoop", "出售完成后立即执行特勤处检查")
                暂停状态显示()
                执行特勤处制造检查("出售完成后立即检查")
                恢复状态显示()
                
                -- 更新状态，确保出售完成后进入挂机状态
                更新状态("上次执行的任务", "挂机", "出售完成后进入挂机状态")
                更新并显示状态面板("✅ 出售后特勤处检查完成，进入挂机状态")
                Logger.info("MainLoop", "出售后特勤处检查完成，已切换到挂机状态")
            else
                -- 2b. 非交易时间，处理特勤处
                local should_check, check_reason = 检查是否需要执行特勤处检查_增强版()
                if should_check and 特勤处制造_功能开关 then
                    Logger.info("MainLoop", string.format("需要执行特勤处检查: %s", check_reason))
                    暂停状态显示()
                    执行特勤处制造检查(check_reason)
                    恢复状态显示()
                    
                    -- 确保执行完特勤处检查后更新状态为挂机
                    更新状态("上次执行的任务", "挂机", "特勤处检查完成后进入挂机状态")
                    更新并显示状态面板("✅ 特勤处检查完成，进入挂机状态")
                    Logger.info("MainLoop", "特勤处检查完成，已切换到挂机状态")
                    
                    if not GameScript.getState("首次检查已完成", false) then
                        GameScript.setState("首次检查已完成", true, "首次运行检查完成")
                    end
                    
                    -- 【修复】只在所有设备都工作时才进入节能模式
                    if State.工作台都在制造 then
                        -- 【核心流程】检查完成后，设置重启任务并关闭游戏
                        local 下次唤醒时间戳 = GameScript.getState("下次制造检查时间戳", 0)
                        if 下次唤醒时间戳 > 当前时间戳 then
                            GameScript.setState("下次任务重启时间", 下次唤醒时间戳, "根据特勤处制造设置")
                            local 时间差 = 下次唤醒时间戳 - 当前时间戳
                            local 小时 = math.floor(时间差 / 3600)
                            local 分钟 = math.floor((时间差 % 3600) / 60)
                            更新并显示状态面板(string.format("✅ 任务设置完成，将在 %d小时%d分钟 后重启游戏", 小时, 分钟))
                        else
                             GameScript.setState("下次任务重启时间", 当前时间戳 + 3600, "无效任务时间后重试")
                             更新并显示状态面板("⚠️ 未获取到有效的下次任务时间，1小时后重试")
                        end
                        
                        更新并显示状态面板("关闭游戏，进入节能模式...")
                        pcall(function() stopApp("com.tencent.tmgp.dfm") end)
                        GameScript.setState("isGameClosed", true, "特勤处检查后进入节能模式")
                    else
                        更新并显示状态面板("⚠️ 检测到仍有空闲制造位或制造失败，暂不进入节能模式，等待下次检查。")
                    end
                else
                    -- 2c. 无任何任务，进入节能模式
                    local 距离上次重启 = 当前时间戳 - GameScript.safeGetNumber(_G.State.上次重启完成时间, 0)
                    
                    -- 【修改】仅交易挂机模式和全功能挂机模式都可以进入节能模式
                    if 自动挂机 and 距离上次重启 > (5 * 60) then
                        -- 判断当前是否在交易时间段
                        local 当前小时 = tonumber(os.date("%H", 当前时间戳))
                        local 出售_开始时间_safe = tonumber(出售_开始时间) or 13
                        local 出售_结束时间_safe = tonumber(出售_结束时间) or 19
                        local 在扫货时间段 = 在时间段内(当前小时, 0, 6)
                        local 在出售时间段 = 在时间段内(当前小时, 出售_开始时间_safe, 出售_结束时间_safe)
                        local 在交易时间段 = 在扫货时间段 or 在出售时间段
                        
                        -- 计算距离下次交易时间
                        local 距离扫货时间_分钟 = 0
                        if 当前小时 >= 6 then
                            距离扫货时间_分钟 = (24 - 当前小时) * 60
                        elseif 当前小时 < 0 then
                            距离扫货时间_分钟 = 0 -- 已经在扫货时间段
                        end
                        
                        -- 只有在非交易时间段且距离下次交易时间超过60分钟才关闭游戏
                        if not 在交易时间段 and 距离扫货时间_分钟 > 60 then
                            -- 仅交易挂机模式：计算下次交易时间
                            if not 特勤处制造_功能开关 then
                                -- 计算距离下次交易时间
                                local 距离扫货时间 = 0
                                if 当前小时 >= 6 then
                                    距离扫货时间 = (24 - 当前小时) * 3600
                                else
                                    距离扫货时间 = 0 -- 已经在扫货时间段
                                end
                                
                                local 距离出售时间 = 0
                                if not 在时间段内(当前小时, 出售_开始时间_safe, 出售_结束时间_safe) then
                                    if 当前小时 < 出售_开始时间_safe then
                                        距离出售时间 = (出售_开始时间_safe - 当前小时) * 3600
                                    else
                                        距离出售时间 = (24 - 当前小时 + 出售_开始时间_safe) * 3600
                                    end
                                else
                                    距离出售时间 = 0 -- 已经在出售时间段
                                end
                                
                                -- 取最近的交易时间作为唤醒时间
                                local 下次唤醒时间 = 0
                                if 距离扫货时间 > 0 and 距离出售时间 > 0 then
                                    下次唤醒时间 = 当前时间戳 + math.min(距离扫货时间, 距离出售时间) - 300 -- 提前5分钟唤醒
                                elseif 距离扫货时间 > 0 then
                                    下次唤醒时间 = 当前时间戳 + 距离扫货时间 - 300
                                elseif 距离出售时间 > 0 then
                                    下次唤醒时间 = 当前时间戳 + 距离出售时间 - 300
                                else
                                    下次唤醒时间 = 当前时间戳 + 3600 -- 默认1小时后唤醒
                                end
                                
                                GameScript.setState("下次任务重启时间", 下次唤醒时间, "仅交易模式下次唤醒")
                                更新并显示状态面板(string.format("仅交易模式：设置下次唤醒时间为 %s", os.date("%H:%M:%S", 下次唤醒时间)))
                            end
                            
                            更新并显示状态面板("当前无任务，进入节能模式...")
                            pcall(function() stopApp("com.tencent.tmgp.dfm") end)
                            GameScript.setState("isGameClosed", true, "无任务进入节能模式")
                        else
                            -- 检查是否是出售完成后的特殊情况
                            if State.上次执行的任务 == "出售完成" and State.强制特勤处检查 then
                                更新并显示状态面板("出售完成后，继续执行特勤处检查...")
                                State.强制特勤处检查 = false
                                -- 这里不进入节能模式，让主循环继续执行特勤处检查
                            else
                                更新并显示状态面板("当前处于交易时间段，不进入节能模式")
                            end
                        end
                    end
                end
            end
        end

        ::continue::
        sleep(sleep_duration)
    end
end

-- 房卡抢购功能
local function 执行房卡抢购()
    if not GameScript.modules.房卡抢购模块 then
        更新并显示状态面板("错误：房卡抢购模块未正确加载")
        return
    end
    
    if GameScript.modules.房卡抢购模块.main then
        更新并显示状态面板("开始执行房卡抢购...")
        local ok, result = pcall(GameScript.modules.房卡抢购模块.main, true)
        if not ok then
            更新并显示状态面板("房卡抢购执行出错: " .. tostring(result))
        end
    else
        更新并显示状态面板("错误：房卡抢购模块缺少main函数")
    end
end

-- 滚仓扫货功能
local function 执行滚仓扫货()
	if not 滚仓扫货 then
		更新并显示状态面板("滚仓功能未启用")
		return false
	end
	
	if not GameScript.modules.滚仓模块 then
		更新并显示状态面板("错误：滚仓模块未加载")
		return false
	end
	
	-- 验证配置
	if State.滚仓_子弹数量 <= 0 or State.滚仓_子弹价格 <= 0 then
		显示信息("错误：滚仓参数无效")
		return false
	end
	
	显示信息("开始执行滚仓功能...")
	显示信息(string.format("配置：数量=%d，价格=%d，总价=%d", 
						  State.滚仓_子弹数量, State.滚仓_子弹价格, State.滚仓_总价格))
	
	-- 确保全局变量正确设置
	_G.滚仓_子弹数量 = State.滚仓_子弹数量
	_G.滚仓_子弹价格 = State.滚仓_子弹价格
	_G.滚仓_总价格 = State.滚仓_总价格
	
	-- 调用滚仓功能
	if type(GameScript.modules.滚仓模块.滚仓功能) == "function" then
		显示信息("正在调用滚仓模块的滚仓功能...")
		local 状态, 结果 = pcall(GameScript.modules.滚仓模块.滚仓功能)
		if 状态 then
			显示信息("✓ 滚仓功能执行完成")
			return true
		else
			显示信息("✗ 滚仓功能执行失败：" .. tostring(结果))
			return false
		end
	else
		显示信息("错误：滚仓模块中未找到滚仓功能函数")
		return false
	end
end

-- 【新增】仅制造挂机模式的主循环
local function 执行仅制造挂机模式()
    Logger.info("MainLoop", "启动仅制造挂机模式")
    local 循环计数 = 0

    while true do
        循环计数 = 循环计数 + 1
        local ok, err = pcall(function()
            -- 每隔一段时间记录一次，避免日志刷屏
            if 循环计数 % 100 == 0 then
                Logger.info("MainLoop", string.format("仅制造循环运行中 - 第%d次循环", 循环计数))
            end

            -- 优先检查定时重启
            local 检查结果 = 修复版时间检查()
            if 检查结果.需要重启检查 and _G.State.启用定时重启 then
                local 需要重启, 重启原因 = 检查是否需要定时重启()
                if 需要重启 then
                    Logger.warn("MainLoop", string.format("触发定时重启: %s", 重启原因))
                    执行定时重启(重启原因)
                    return -- 重启后将重新开始循环
                end
            end

            -- 核心逻辑：只检查和执行特勤处任务
            -- 【修复】优先检查节能模式状态，避免在节能模式下执行特勤处检查
            if _G.State and _G.State.isGameClosed then
                -- 节能模式下，只做心跳日志
                if 检查结果.当前时间戳 >= (_G.State.下次心跳时间 or 0) then
                    local next_check_ts = _G.State.下次制造检查时间戳 or (检查结果.当前时间戳 + 900)
                    local next_check_time_str = os.date("%H:%M:%S", next_check_ts)
                    显示信息(string.format("💤 仅制造节能模式运行中... 下次检查在 %s", next_check_time_str), false)
                    更新状态("下次心跳时间", 检查结果.当前时间戳 + 300, "仅制造模式心跳")
                end
                
                -- 【修复】检查是否需要唤醒进行特勤处检查
                local 下次检查时间戳 = _G.State.下次制造检查时间戳 or 0
                -- 【新增】保护机制：确保时间戳合理（至少10分钟后）
                local 最小等待时间 = 10 * 60 -- 10分钟
                local 时间戳是否合理 = (下次检查时间戳 - 检查结果.当前时间戳) >= 最小等待时间
                
                if 下次检查时间戳 > 0 and 检查结果.当前时间戳 >= 下次检查时间戳 and 时间戳是否合理 then
                    显示信息("🔄 到达下次制造检查时间，从节能模式唤醒...")
                    更新状态("isGameClosed", false, "仅制造模式唤醒")
                    检查并启动游戏()
                    执行特勤处制造检查("仅制造模式唤醒检查")
                else
                    -- 【新增】详细调试信息
                    if _G.State.isGameClosed and 下次检查时间戳 > 0 then
                        local 当前时间戳 = 检查结果.当前时间戳
                        local 距离下次检查 = 下次检查时间戳 - 当前时间戳
                        显示信息(string.format("💤 仅制造节能模式调试：当前时间=%s, 下次检查时间=%s, 距离=%d秒, 时间戳合理=%s", 
                                              os.date("%H:%M:%S", 当前时间戳),
                                              os.date("%H:%M:%S", 下次检查时间戳),
                                              距离下次检查,
                                              时间戳是否合理 and "是" or "否"), false)
                    end
                end
            else
                -- 非节能模式：正常执行特勤处检查
            local should_check, check_reason = 检查是否需要执行特勤处检查_增强版()
            if should_check then
                Logger.info("MainLoop", string.format("需要执行特勤处检查: %s", check_reason))
                暂停状态显示()
                检查并启动游戏()
                执行特勤处制造检查(check_reason)
                恢复状态显示()
                Logger.info("MainLoop", "特勤处检查完成")
            else
                -- 空闲时，更新状态显示
                if 检查结果.需要更新状态 then
                    显示任务状态()
                    _G.State.下次状态更新时间 = 检查结果.当前时间戳 + 60
                    end
                end
            end
        end)
        
        if not ok then
            Logger.error("MainLoop", string.format("仅制造循环发生错误: %s", tostring(err)))
            全局错误处理(err, "仅制造挂机")
        end
        
        sleep(3000) -- 标准循环延迟
    end
end

-- =================== 主程序流程 ===================
local function 主程序执行()
    脚本启动处理()
    
    -- 【修复】移除已失效的旧版状态同步逻辑，该逻辑在新框架下会导致崩溃
    
    -- 记录脚本启动信息
    GameScript.setState("脚本版本", GameScript.version, "初始化")
    GameScript.setState("脚本启动时间", os.time(), "初始化")
    
    -- 【修复2】重新设计模式选择逻辑，明确优先级
    local function 确定执行模式()
        -- 统计已启用的主要功能
        local 启用的功能 = {}
        if 扫货功能 then table.insert(启用的功能, "扫货") end
        if 房卡功能 then table.insert(启用的功能, "房卡") end
        if 五级蛋功能 then table.insert(启用的功能, "五级蛋") end
        if 出售子弹 then table.insert(启用的功能, "出售") end
        if 单机抢购模式 then table.insert(启用的功能, "单机抢购") end -- 将单机抢购加入功能列表
        
        显示信息("检测到的主要功能: " .. (#启用的功能 > 0 and table.concat(启用的功能, ", ") or "无"))
        显示信息("自动挂机状态: " .. tostring(自动挂机))
        显示信息("特勤处制造状态: " .. tostring(特勤处制造_功能开关))

        -- 规则1：如果勾选了"自动挂机"，则它拥有最高优先级
	if 自动挂机 then
		if 特勤处制造_功能开关 then
                return "全功能挂机模式"
            else
                return "仅交易挂机模式"
            end
        end

        -- 规则2：如果没有勾选"自动挂机"，处理单选功能
        -- 规则2.1：处理"仅制造"模式
        if 特勤处制造_功能开关 and #启用的功能 == 0 then
            return "仅制造模式"
        end

        -- 规则2.2：处理其他单选的主要功能
        if #启用的功能 == 1 then
            if 扫货功能 then return "单独扫货模式" end
            if 房卡功能 then return "房卡抢购模式" end
            if 五级蛋功能 then return "五级蛋模式" end
            if 出售子弹 then return "单独出售模式" end
            if 单机抢购模式 then return "单机抢购模式" end -- 明确返回单机抢购模式
        end
        
        -- 规则3：处理多选情况（非挂机模式）
        if #启用的功能 > 1 then
            return "功能冲突"
        end

        -- 规则4：未选择任何功能
        return "未选择任何功能"
    end

    -- 【修复3】根据确定的模式执行相应逻辑
    local 执行模式 = 确定执行模式()
    显示信息("🎯 执行模式: " .. 执行模式)
    
    -- 记录执行模式到框架状态
    GameScript.setState("执行模式", 执行模式, "模式确定")

    if 执行模式 == "全功能挂机模式" then
        Logger.info("Main", "启动【增强版全功能】自动挂机模式（扫货、出售、特勤处制造）")
			执行全功能挂机模式_增强版()
    elseif 执行模式 == "仅交易挂机模式" then
        Logger.info("Main", "启动【仅交易】自动挂机模式（扫货、出售）")
        执行全功能挂机模式_增强版() -- 复用同一个主循环，内部逻辑会处理
    elseif 执行模式 == "仅制造模式" then
        Logger.info("Main", "启动【仅制造】智能挂机模式")
        执行仅制造挂机模式()
    elseif 执行模式 == "单独扫货模式" then
		Logger.info("Main", "启动单独扫货模式")
		执行扫货抢购(false, State.扫货输入价格)
    elseif 执行模式 == "房卡抢购模式" then
		Logger.info("Main", "启动房卡抢购模式")
		执行房卡抢购()
    elseif 执行模式 == "五级蛋模式" then
        Logger.info("Main", "启动五级蛋模式")
        if GameScript.modules.五级蛋抢购模块 and GameScript.modules.五级蛋抢购模块.main then
            -- 【修复】设置五级蛋专用的全局变量
            _G.五级蛋输入价格 = State.五级蛋价格
            GameScript.modules.五级蛋抢购模块.main(false, State.五级蛋价格)
        else
            显示信息("❌ 错误：五级蛋抢购模块未正确加载或缺少main函数！")
        end
    elseif 执行模式 == "单独出售模式" then
		Logger.info("Main", "启动【持续】单独出售模式")
		if not GameScript.modules.出售子弹模块 then
			显示信息("错误：出售模块未加载，无法执行。")
			return
		end
	
		-- 改进单独出售模式
		local 连续失败次数 = 0
		local 最大连续失败次数 = 10
		
		-- 【新增】显示出售配置信息
		local 子弹类型名称 = {
			["0"] = "9.19子弹",
			["1"] = "5.7子弹", 
			["2"] = ".45ACP子弹",
			["3"] = "12 Gauge子弹",
			["4"] = ".300 BLk子弹"
		}
		local 当前子弹类型 = 子弹类型名称[tostring(_G.多选框_出售子弹_type)] or "未知类型"
		显示信息(string.format("=== 单独出售模式启动 ==="))
		显示信息(string.format("出售类型: %s", 当前子弹类型))
		显示信息(string.format("出售价格: %d元", 输入框_出售价格))
		显示信息("模式: 持续出售直到售罄")
	
		while true do
			local funcs_map = {["0"] = "自动出售9_19", ["1"] = "自动出售57_28", ["2"] = "自动出售45ACP", ["3"] = "自动出售12_Gauge", ["4"] = "自动出售300_BLK"}
			local func_to_call = funcs_map[_G.多选框_出售子弹_type]
			if func_to_call and GameScript.modules.出售子弹模块 and GameScript.modules.出售子弹模块[func_to_call] then
				local ok, sell_result = pcall(GameScript.modules.出售子弹模块[func_to_call], 输入框_出售价格, State)
				if ok and sell_result then
					local 状态码 = sell_result.状态 or sell_result
					显示信息("单独出售模式 - 状态码: " .. tostring(状态码))
				
					-- 检查售罄状态
					if 状态码 == "SOLD_OUT" or 状态码 == "售罄" or tostring(状态码):find("售罄") then
						显示信息("✅ 所有子弹均已售罄，单独出售模式完成，脚本将退出。")
						return -- 成功退出
					elseif 状态码 == "SUCCESS" or 状态码 == "成功" then
						显示信息("✅ 单轮出售成功，继续下一轮...")
						连续失败次数 = 0 -- 重置失败计数
					else
						连续失败次数 = 连续失败次数 + 1
						显示信息(string.format("⚠️ 出售状态异常: %s (连续失败: %d/%d)", 
											tostring(状态码), 连续失败次数, 最大连续失败次数))
					end
				elseif not ok then
					连续失败次数 = 连续失败次数 + 1
					显示信息(string.format("!! 独立出售模式发生错误: %s (连续失败: %d/%d)", 
										  tostring(sell_result), 连续失败次数, 最大连续失败次数))
				end
			
				-- 连续失败过多时退出，避免无限循环
				if 连续失败次数 >= 最大连续失败次数 then
					显示信息(string.format("❌ 连续失败达到%d次，可能出现严重问题，脚本将退出", 最大连续失败次数))
					return
				end
			else
				显示信息("错误：无效的子弹类型选项：" .. tostring(_G.多选框_出售子弹_type))
				sleep(30000)
			end
			sleep(2000) -- 增加间隔，避免过于频繁的检查
		end
    elseif 执行模式 == "滚仓扫货模式" then
		Logger.info("Main", "启动单独滚仓模式")
		执行滚仓扫货()
    elseif 执行模式 == "单机抢购模式" then
        Logger.info("Main", "启动单机抢购模式")
        if GameScript.modules.单机抢购模式模块 and GameScript.modules.单机抢购模式模块.main then
            -- 确保配置中包含单机抢购延迟
            当前配置.输入框_单机抢购延迟 = 输入框_单机抢购延迟
            GameScript.modules.单机抢购模式模块.main(当前配置)
        else
            显示信息("❌ 错误：单机抢购模式模块未正确加载或缺少main函数！")
        end
    elseif 执行模式 == "功能冲突" then
        Logger.error("Main", "功能冲突，请只选择一个主要功能（或使用自动挂机）")
        显示信息("❌ 功能冲突：请不要在非[自动挂机]模式下同时勾选多个主要功能（如扫货+出售）。")
        return
    else -- 未选择任何功能
		Logger.warn("Main", "未选择任何功能，脚本将退出")
        显示信息("⚠️ 未选择任何有效的功能组合，请检查配置。")
		return
	end
end

-- =================== 主程序启动入口 ===================
-- 启动主程序
local 启动状态, 启动错误 = pcall(主程序执行)
if not 启动状态 then
	Logger.fatal("Main", string.format("主程序启动失败: %s", tostring(启动错误)))
	显示信息("严重错误：主程序启动失败 - " .. tostring(启动错误))
end

-- 确保脚本结束时清理日志
脚本结束处理()
