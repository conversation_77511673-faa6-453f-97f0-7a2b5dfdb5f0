# 单机抢购延迟功能修复

## 问题描述
用户设置了30秒的单机抢购延迟，但是脚本没有生效，价格过高时立即进入下一轮循环。

## 根本原因
1. UI配置中有"输入框_单机抢购延迟"字段
2. 主脚本没有正确读取这个配置
3. 配置传递给单机抢购模块时变量作用域问题

## 修复措施

### 1. 添加UI配置读取 (三角洲行动定制.lua)
```lua
输入框_单机抢购延迟 = 安全读取UI值(ui_page1, "输入框_单机抢购延迟", "0"), -- 【修复】读取单机抢购延迟配置
```

### 2. 修复配置传递 (三角洲行动定制.lua)
```lua
-- 【修复】确保配置中包含单机抢购延迟，从界面配置中读取
当前配置.输入框_单机抢购延迟 = 界面配置.输入框_单机抢购延迟 or "0"
显示信息(string.format("🔧 单机抢购延迟配置: %s秒", 当前配置.输入框_单机抢购延迟))
```

## 延迟逻辑说明

单机抢购模式中的延迟逻辑：
- ✅ 价格合适时：立即继续购买，无延迟
- ✅ 价格过高时：使用自定义延迟（如30秒）
- ✅ 未设置延迟时：使用默认300ms延迟

## 使用方法
1. 在UI界面的"延迟"输入框中设置秒数（如30）
2. 启动单机抢购模式
3. 当价格过高时，脚本会等待设置的秒数后再继续

## 修复效果
- ✅ 正确读取UI中的延迟配置
- ✅ 配置正确传递给单机抢购模块
- ✅ 价格过高时执行自定义延迟
- ✅ 显示延迟配置信息便于调试

现在重新运行脚本，30秒延迟应该会生效！
