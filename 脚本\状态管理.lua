-- 三角洲行动定制 - 改进的状态管理模块
-- 版本: 1.0.0
-- 功能: 提供统一的状态管理、验证、事件监听和历史记录

local StateManager = {}

-- 获取全局框架引用（如果可用）
local GameScript = _G.GameScript
local Logger = _G.Logger

-- 状态管理器版本
StateManager.version = "1.0.0"

-- 导出验证器供外部使用
StateManager.StateValidators = StateValidators

-- 状态验证器（导出供外部使用）
local StateValidators = {
    数值 = function(value, min, max)
        local num = tonumber(value)
        if not num then return false, "不是有效数字" end
        if min and num < min then return false, string.format("小于最小值 %d", min) end
        if max and num > max then return false, string.format("大于最大值 %d", max) end
        return true, num
    end,
    
    布尔值 = function(value)
        if type(value) == "boolean" then return true, value end
        if type(value) == "string" then
            local lower = string.lower(value)
            if lower == "true" or lower == "1" or lower == "yes" then
                return true, true
            elseif lower == "false" or lower == "0" or lower == "no" then
                return true, false
            end
        end
        if type(value) == "number" then
            return true, value ~= 0
        end
        return false, "无法转换为布尔值"
    end,
    
    字符串 = function(value, allowed_values)
        local str = tostring(value)
        if allowed_values then
            for _, allowed in ipairs(allowed_values) do
                if str == allowed then
                    return true, str
                end
            end
            return false, string.format("值'%s'不在允许的列表中", str)
        end
        return true, str
    end,
    
    时间戳 = function(value)
        local timestamp = tonumber(value)
        if not timestamp then return false, "无效时间戳" end
        if timestamp < 0 then return false, "时间戳不能为负数" end
        return true, timestamp
    end,
    
    表 = function(value)
        if type(value) ~= "table" then
            return false, "不是有效的表"
        end
        return true, value
    end
}

-- 状态模式定义
StateManager.Schema = {
    -- 基础配置
    扫货输入价格 = {validator = "数值", min = 1, max = 999999999, default = 630},
    房卡输入价格 = {validator = "数值", min = 1, max = 999999999, default = 630},
    五级蛋价格 = {validator = "数值", min = 1, max = 999999999, default = 150000},

    -- 功能开关
    全自动挂机模式 = {validator = "布尔值", default = false},
    资金不足 = {validator = "布尔值", default = false},
    所有子弹已售罄 = {validator = "布尔值", default = false},
    isGameClosed = {validator = "布尔值", default = false},
    工作台都在制造 = {validator = "布尔值", default = false},
    允许状态显示 = {validator = "布尔值", default = true},
    初始检查完成 = {validator = "布尔值", default = false},
    首次检查已完成 = {validator = "布尔值", default = false},
    重启进行中 = {validator = "布尔值", default = false},
    启用定时重启 = {validator = "布尔值", default = true},

    -- 功能状态
    上次执行的任务 = {validator = "字符串", default = ""},
    当前执行模式 = {validator = "字符串", default = "未设置"},
    当前动作描述 = {validator = "字符串", default = "脚本初始化中..."},
    缓存状态信息 = {validator = "字符串", default = ""},

    -- 时间相关
    脚本启动时间 = {validator = "时间戳", default = function() return os.time() end},
    下次状态更新时间 = {validator = "时间戳", default = function() return os.time() + 5 end},
    上次重启完成时间 = {validator = "时间戳", default = function() return os.time() end},
    上次特勤处全面检查时间 = {validator = "时间戳", default = 0},
    下次制造检查时间戳 = {validator = "时间戳", default = 0},
    下次心跳时间 = {validator = "时间戳", default = function() return os.time() + 300 end},
    重启状态锁定时间 = {validator = "时间戳", default = 0},
    上次重启检查时间 = {validator = "时间戳", default = 0},
    上次状态计算时间 = {validator = "时间戳", default = 0},

    -- 计数器
    重启计数 = {validator = "数值", min = 0, default = 0},
    交易行查找连续失败次数 = {validator = "数值", min = 0, default = 0},
    成功出售次数 = {validator = "数值", min = 0, default = 0},
    当前制造中数量 = {validator = "数值", min = 0, default = 0},
    当前空闲数量 = {validator = "数值", min = 0, default = 4},
    抢购次数 = {validator = "数值", min = 0, default = 1},
    定时重启间隔 = {validator = "数值", min = 60, default = 5 * 60 * 60}, -- 默认5小时
    重启状态最大持续时间 = {validator = "数值", min = 10, default = 60},
    重启检查冷却时间 = {validator = "数值", min = 10, default = 30},

    -- 复杂对象
    空闲设备列表 = {validator = "表", default = function() return {} end},
    特勤处检查锁 = {validator = "表", default = function() return {
        正在检查中 = false,
        上次检查完成时间 = 0,
        最小检查间隔 = 180
    } end}
}

-- 创建状态管理器实例
function StateManager.create(initial_state)
    local instance = {
        _state = {},        -- 当前状态存储
        _listeners = {},    -- 状态监听器
        _history = {},      -- 状态变更历史
        _locked = {}        -- 锁定的状态
    }
    
    -- 初始化状态
    for key, schema in pairs(StateManager.Schema) do
        local default_value = schema.default
        if type(default_value) == "function" then
            default_value = default_value()
        end
        instance._state[key] = default_value
    end
    
    -- 应用初始状态
    if initial_state then
        for key, value in pairs(initial_state) do
            instance:set(key, value, "初始化")
        end
    end
    
    -- 如果Logger可用，记录初始化信息
    if Logger then
        Logger.info("StateManager", "状态管理器初始化完成", {
            total_fields = #StateManager.Schema,
            version = StateManager.version
        })
    end
    
    return setmetatable(instance, {__index = StateManager})
end

-- 获取状态值
function StateManager:get(key, default_value)
    if self._state[key] ~= nil then
        return self._state[key]
    end
    
    -- 尝试从Schema获取默认值
    if StateManager.Schema[key] and StateManager.Schema[key].default ~= nil then
        local default = StateManager.Schema[key].default
        if type(default) == "function" then
            default = default()
        end
        return default
    end
    
    return default_value
end

-- 设置状态值（带验证）
function StateManager:set(key, value, reason)
    -- 检查是否被锁定
    if self._locked[key] then
        if Logger then
            Logger.warn("StateManager", string.format("尝试修改被锁定的状态: %s", key))
        end
        return false
    end
    
    -- 验证值
    local schema = StateManager.Schema[key]
    if schema and schema.validator then
        local validator = StateValidators[schema.validator]
        if validator then
            local valid, validated_value = validator(value, schema.min, schema.max)
            if not valid then
                if Logger then
                    Logger.error("StateManager", string.format("状态验证失败 %s: %s", key, validated_value))
                end
                return false
            end
            value = validated_value
        end
    end
    
    -- 记录历史
    local old_value = self._state[key]
    if old_value ~= value then
        table.insert(self._history, {
            timestamp = os.time(),
            key = key,
            old_value = old_value,
            new_value = value,
            reason = reason or "未知原因"
        })
        
        -- 限制历史记录长度
        if #self._history > 1000 then
            table.remove(self._history, 1)
        end
        
        -- 记录状态变化
        if Logger then
            Logger.stateChange("StateManager", 
                              string.format("%s=%s", key, tostring(old_value)),
                              string.format("%s=%s", key, tostring(value)),
                              reason or "状态更新")
        end
        
        -- 通知监听器
        self:_notifyListeners(key, old_value, value, reason)
    end
    
    self._state[key] = value
    
    -- 与旧版State同步（向后兼容）
    if _G.State then
        _G.State[key] = value
    end
    
    -- 与GameScript框架集成 (使用新版setState)
    if GameScript and GameScript.setState then
        GameScript.setState(key, value, "来自StateManager的同步")
    end
    
    return true
end

-- 锁定状态（防止修改）
function StateManager:lock(key, reason)
    self._locked[key] = {
        timestamp = os.time(),
        reason = reason or "手动锁定"
    }
    
    if Logger then
        Logger.info("StateManager", string.format("状态已锁定: %s (%s)", key, reason or "手动锁定"))
    end
end

-- 解锁状态
function StateManager:unlock(key, reason)
    if self._locked[key] then
        self._locked[key] = nil
        if Logger then
            Logger.info("StateManager", string.format("状态已解锁: %s (%s)", key, reason or "手动解锁"))
        end
    end
end

-- 添加状态监听器
function StateManager:addListener(key, callback)
    if not self._listeners[key] then
        self._listeners[key] = {}
    end
    table.insert(self._listeners[key], callback)
    
    if Logger and Logger.debug then
        Logger.debug("StateManager", string.format("添加监听器: %s", key))
    end
end

-- 通知监听器
function StateManager:_notifyListeners(key, old_value, new_value, reason)
    local listeners = self._listeners[key]
    if listeners then
        for _, callback in ipairs(listeners) do
            -- 使用pcall确保一个监听器的错误不会影响其他监听器
            local success, err = pcall(callback, key, old_value, new_value, reason)
            if not success and Logger then
                Logger.error("StateManager", string.format("监听器错误 [%s]: %s", key, tostring(err)))
            end
        end
    end
end

-- 获取状态快照
function StateManager:getSnapshot()
    local snapshot = {}
    for key, value in pairs(self._state) do
        snapshot[key] = value
    end
    return snapshot
end

-- 批量更新状态
function StateManager:batch(updates, reason)
    local success = true
    local batch_reason = reason or "批量更新"
    
    for key, value in pairs(updates) do
        if not self:set(key, value, batch_reason) then
            success = false
        end
    end
    
    return success
end

-- 获取状态历史
function StateManager:getHistory(max_entries, filter_key)
    local result = {}
    local count = 0
    local total = #self._history
    
    -- 从最新的开始
    for i = total, 1, -1 do
        local entry = self._history[i]
        if not filter_key or entry.key == filter_key then
            table.insert(result, entry)
            count = count + 1
            if max_entries and count >= max_entries then
                break
            end
        end
    end
    
    return result
end

-- 检查是否存在键
function StateManager:has(key)
    return self._state[key] ~= nil
end

-- 获取锁定状态
function StateManager:isLocked(key)
    return self._locked[key] ~= nil
end

-- 迁移传统状态
function StateManager:migrateFromLegacy(legacy_state)
    if not legacy_state then return false end
    
    local migrated_count = 0
    
    for key, schema in pairs(StateManager.Schema) do
        if legacy_state[key] ~= nil then
            self:set(key, legacy_state[key], "从旧状态迁移")
            migrated_count = migrated_count + 1
        end
    end
    
    if Logger then
        Logger.info("StateManager", string.format("从旧状态迁移了 %d 个值", migrated_count))
    end
    
    return migrated_count > 0
end

-- 重置状态到默认值
function StateManager:reset(keys, reason)
    local reset_reason = reason or "重置状态"
    
    -- 如果提供了特定的键列表，只重置这些键
    if keys then
        for _, key in ipairs(keys) do
            local schema = StateManager.Schema[key]
            if schema then
                local default_value = schema.default
                if type(default_value) == "function" then
                    default_value = default_value()
                end
                self:set(key, default_value, reset_reason)
            end
        end
    else
        -- 否则重置所有键
        for key, schema in pairs(StateManager.Schema) do
            if not self:isLocked(key) then
                local default_value = schema.default
                if type(default_value) == "function" then
                    default_value = default_value()
                end
                self:set(key, default_value, reset_reason)
            end
        end
    end
end

-- =================== 与GameScript框架集成 ===================
-- 如果GameScript可用，注册到GameScript
if GameScript then
    GameScript.modules.StateManager = StateManager
    if GameScript.registerGlobal then
        GameScript.registerGlobal("StateManager", StateManager, "核心框架")
    end
end

-- 返回模块
return StateManager 